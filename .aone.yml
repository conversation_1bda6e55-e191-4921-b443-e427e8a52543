stage:
  h5-unit-test:
    env:
      image: 'aone-base-global/alios7-nodejs-ci:1.0.0'
    plugin:
      -
        param:
          url: '${repo}'
          branch: '${branch}'
          path: '${source_root}'
          isDelGit: 'false'
        name: checkout
        pos: front
    exec:
      - export PATH=$PWD/node_modules/.bin:$HOME/.cli:$PATH
      - echo $PATH
      # 如果你在项目里指定了 tnpm.mode = yarn, 则需要手动安装一下
      - npm i -g yarn
      # 镜像里 tnpm 版本比较低，升级一下
      - tnpm install -g tnpm@latest

      - tnpm i
      # 如果你是 ts 项目，可能需要做一些前置编译
      # - tnpm run build
      # aone ci 上 remote url 为 http://apposs:<EMAIL>/smart-app/e-cspace-mini.git，单测框架不识别，强行 set 一下
      - git remote set-<NAME_EMAIL>:alimail-frontend/webmail4.git
      - tnpm i @ali/dtest-toolbox --registry=$NPM_REGISTRY -g
      - tnpm run test
      - tnpm run test-prepush
      # 会出现 “[ERROR] 单测数据获取失败，请重新push，并保证有单测用例” 再跑一次 (*)
      - tnpm run test-prepush
pipeline:
  - h5-unit-test