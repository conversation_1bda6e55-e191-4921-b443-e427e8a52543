---
globs: src/apis/*.ts,src/services/*.ts
---

# API 开发规范

## API 结构
基础配置在 [src/apis/base.ts](mdc:src/apis/base.ts) 中定义，所有 API 接口都应该基于此配置。

## API 文件组织
- `src/apis/` - 公共 API 接口
- `src/pages/*/services/` - 页面特定的服务接口

## 请求封装规范

### 基础请求
```typescript
import { request } from '@/apis/base';

// GET 请求
export const getProductList = (params: any) =>
  request.get('/api/products', { params });

// POST 请求
export const createProduct = (data: any) =>
  request.post('/api/products', data);
```

### 类型定义
```typescript
interface ProductListRequest {
  page: number;
  size: number;
  category?: string;
}

interface ProductListResponse {
  data: Product[];
  total: number;
  page: number;
}

export const getProductList = (params: ProductListRequest): Promise<ProductListResponse> =>
  request.get('/api/products', { params });
```

## 错误处理
- 统一在 base.ts 中处理通用错误
- 页面级错误在组件中处理
- 使用 try-catch 处理异步请求

## 数据模拟
- 使用 `src/data/` 目录中的数据进行开发测试
- 参考 [productData.ts](mdc:src/data/productData.ts) 等文件

## 最佳实践
- 所有 API 请求都要有类型定义
- 使用 axios 进行 HTTP 请求
- 统一的错误处理机制
- 支持请求拦截器和响应拦截器
