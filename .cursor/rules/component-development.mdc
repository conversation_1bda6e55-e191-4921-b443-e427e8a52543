---
globs: src/components/**/*.tsx
---

# 组件开发规范

## 组件结构
每个组件目录包含：
- `index.tsx` - 组件实现
- `index.less` - 组件样式
- `README.md` - 组件文档（可选）

## 组件类型

### 基础组件 (src/components/)
公共可复用组件，例如：
- [Loading](mdc:src/components/Loading/index.tsx) - 加载指示器
- [OptimizedImage](mdc:src/components/OptimizedImage/index.tsx) - 优化图片组件
- [ImageSwiper](mdc:src/components/ImageSwiper/index.tsx) - 图片轮播
- [RateItemWithPercent](mdc:src/components/RateItemWithPercent/index.tsx) - 评分组件

### 页面组件 (src/pages/*/components/)
特定页面的私有组件

## 组件开发规范

### 组件定义模板
```typescript
import React, { FC } from 'react';
import './index.less';

interface ComponentProps {
  // 定义组件属性
  title?: string;
  loading?: boolean;
  onClick?: () => void;
}

const Component: FC<ComponentProps> = ({
  title = '默认标题',
  loading = false,
  onClick
}) => {
  // 组件逻辑

  return (
    <div className="component-wrapper">
      {/* 组件内容 */}
    </div>
  );
};

export default Component;
```

### 样式规范
```less
.component-wrapper {
  // 组件根容器样式

  .component {
    &-title {
      // 标题样式
    }

    &-content {
      // 内容样式
    }
  }
}
```

## 组件最佳实践

### 性能优化
- 使用 React.memo 避免不必要的重渲染
- 使用 useMemo 和 useCallback 优化复杂计算
- 图片懒加载使用 react-lazy-load-image-component

### 可访问性
- 使用语义化的 HTML 标签
- 添加适当的 aria 属性
- 支持键盘导航

### 错误处理
- 使用 ErrorBoundary 包装复杂组件
- 提供回退 UI
- 记录错误日志

### 国际化支持
- 所有文本都要支持国际化
- 考虑不同语言的布局影响

## 组件文档
- 提供清晰的 Props 接口说明
- 包含使用示例
- 说明组件的设计意图和使用场景
