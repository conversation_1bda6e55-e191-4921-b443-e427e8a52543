---
globs: src/i18n/*.js,src/pages/**/index.tsx
---

# 国际化 (i18n) 开发规范

## 支持的语言
- 中文 (zh_CN)
- 英文 (en_US)
- 日文 (ja_JP)

## 配置文件
- [src/i18n/translation_zh_CN.js](mdc:src/i18n/translation_zh_CN.js) - 中文翻译
- [src/i18n/translation_en_US.js](mdc:src/i18n/translation_en_US.js) - 英文翻译
- [src/i18n/translation_ja_JP.js](mdc:src/i18n/translation_ja_JP.js) - 日文翻译

## 使用规范

### 在组件中使用国际化
```typescript
import { i18next } from '@ali/dingtalk-i18n';

const Component = () => {

  return (
    <div>
      <h1>{i18next.t('common.title')}</h1>
      <p>{i18next.t('product.description')}</p>
    </div>
  );
};
```

### 翻译键命名规范
```javascript
// 按模块分组
export default {
  common: {
    title: '标题',
    confirm: '确认',
    cancel: '取消',
  },
  product: {
    name: '产品名称',
    price: '价格',
    detail: '详情',
  },
  error: {
    network: '网络错误',
    permission: '权限不足',
  }
};
```

## 开发流程
1. 在 [translationKeys.js](mdc:translationKeys.js) 中定义翻译键
2. 运行 `npm run update-i18n` 更新翻译文件
3. 在各语言文件中补充翻译内容
4. 在组件中使用 `i18next.t()` 函数获取翻译

## 最佳实践
- 翻译键使用小写字母和点号分隔
- 避免硬编码文本，所有用户可见文本都要国际化
- 使用有意义的键名
- 考虑不同语言的文本长度差异
- 数字和日期格式化要考虑地区差异
