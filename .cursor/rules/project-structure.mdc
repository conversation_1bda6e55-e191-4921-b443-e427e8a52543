---
alwaysApply: true
---

# J项目Agent选品仓库 - 项目结构规则

## 项目概述
这是一个基于 React 18.3.1 + TypeScript 的电商选品系统，主要用于产品管理和选品功能。

## 目录结构说明

### 核心目录
- `src/pages/` - 页面组件，每个页面都有独立的 app.tsx 入口文件
- `src/components/` - 公共组件库
- `src/apis/` - API 接口定义，使用 [base.ts](mdc:src/apis/base.ts) 作为基础配置
- `src/common/` - 公共资源，包含全局样式和类型定义
- `src/utils/` - 工具函数库
- `src/i18n/` - 国际化资源文件

### 页面结构规范
每个页面目录包含：
- `app.tsx` - 页面入口文件
- `index.tsx` - 主组件
- `index.less` - 样式文件
- `components/` - 页面私有组件
- `services/` - 页面相关服务
- `types/` - 页面相关类型定义

### 路径别名
- `@/*` 映射到 `src/*`，在导入时使用绝对路径

## 技术栈
- React 18.3.1 + TypeScript
- Ant Design 5.x (antd)
- Recoil (状态管理)
- Less + Tailwind CSS
- 钉钉 JSAPI
- 国际化支持 (i18n)

## 开发规范
- 所有组件必须使用 TypeScript
- 使用 ESLint 和 stylelint 进行代码检查
- 遵循 f2elint 规范
- 支持中文、英文、日文三种语言
