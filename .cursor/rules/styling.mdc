---
globs: *.less,*.css
---

# 样式开发规范

## 样式技术栈
- Less 预处理器
- Tailwind CSS 工具类
- CSS Modules 支持
- Ant Design 主题定制
- 钉钉主题库 (dingtalk-theme)

## 文件结构
- `src/common/styles/global.less` - 全局样式
- 每个组件对应一个 .less 文件
- 使用 CSS Modules 避免样式冲突

## 钉钉主题库规范

### 引入规范
```less
// 每个 .less 文件开头必须引入钉钉主题库
@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";
```

### 颜色变量使用
```less
// 禁止使用硬编码颜色值，必须使用钉钉主题变量
// ❌ 错误示例
color: #333;
background-color: #f5f5f5;

// ✅ 正确示例
color: @common_level1_base_color;
background-color: @common_bg_color;
```

### 文字样式使用
```less
// 禁止使用硬编码字体大小，必须使用钉钉主题 mixin
// ❌ 错误示例
font-size: 14px;
line-height: 1.5;

// ✅ 正确示例
.common_body_text_style_mob();
.common_action_bold_text_style_mob();
```

### 圆角使用
```less
// 禁止使用硬编码圆角值，必须使用钉钉主题变量
// ❌ 错误示例
border-radius: 4px;
border-radius: 8px;

// ✅ 正确示例
border-radius: @common_border_radius_s;
border-radius: @common_border_radius_m;
border-radius: @common_border_radius_l;
```

### 阴影使用
```less
// 禁止使用硬编码阴影值，必须使用钉钉主题 mixin
// ❌ 错误示例
box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// ✅ 正确示例
.common_box_shadow_s();
.common_box_shadow_m();
.common_box_shadow_l();
```

### 动画使用
```less
// 禁止使用硬编码动画时间，必须使用钉钉主题变量
// ❌ 错误示例
transition: all 0.3s ease;

// ✅ 正确示例
transition: all @common_light_motion_duration @common_light_motion_timing_function;
```

## Less 规范

### 变量定义
```less
// 颜色变量 - 使用钉钉主题变量
@primary-color: @theme_primary1_color;
@success-color: @common_green1_color;
@error-color: @theme_danger1_color;

// 尺寸变量 - 使用钉钉主题变量
@border-radius: @common_border_radius_m;
@box-shadow: @common_box_shadow_m;
```

### 嵌套规则
```less
.component {
  padding: 16px;

  &-title {
    .common_h2_bold_text_style_mob();
    color: @common_level1_base_color;
  }

  &-content {
    margin-top: 12px;

    .item {
      margin-bottom: 8px;
      .common_body_text_style_mob();
    }
  }
}
```

## 移动端适配
- 使用 px 单位进行布局设计
- 通过媒体查询实现响应式布局
- 支持 iOS >= 7, Android >= 4
- 针对钉钉容器进行优化
- 使用钉钉主题的移动端 mixin

### 响应式设计规范
```less
// 基础样式（移动端优先）
.component {
  padding: 16px;
  font-size: 14px;

  // 平板端适配
  @media (min-width: 768px) {
    padding: 20px;
    font-size: 16px;
  }

  // 桌面端适配
  @media (min-width: 1024px) {
    padding: 24px;
    font-size: 18px;
  }
}
```

## Tailwind CSS 集成
- 与 Less 混合使用
- 优先使用 Tailwind 工具类
- 复杂样式使用 Less 编写
- 颜色和尺寸优先使用钉钉主题变量

## 最佳实践
- 使用 BEM 命名规范
- 避免深层嵌套（不超过3层）
- 使用 CSS Modules 避免全局污染
- 遵循 stylelint 规则
- 支持暗色模式（钉钉主题自动支持）
- **强制使用钉钉主题变量，禁止硬编码颜色、字体、圆角等值**
- 使用钉钉主题的 mixin 函数来设置文字样式
- 使用钉钉主题的动画变量来设置过渡效果

## 常用钉钉主题变量

### 颜色变量
```less
// 基础颜色
@common_level1_base_color  // 主要文字
@common_level2_base_color  // 次要文字
@common_level3_base_color  // 辅助文字
@common_level4_base_color  // 占位文字
@common_bg_color          // 背景色
@common_bg_z1_color       // 卡片背景
@theme_primary1_color     // 主色调
@theme_danger1_color      // 危险色
```

### 文字样式 Mixin
```less
.common_supertitle_text_style_mob()      // 超级标题
.common_largetitle_text_style_mob()      // 大标题
.common_h1_text_style_mob()              // 一级标题
.common_h2_text_style_mob()              // 二级标题
.common_body_text_style_mob()            // 正文
.common_action_text_style_mob()          // 操作文字
.common_footnote_text_style_mob()        // 脚注
.common_tiny_text_style_mob()            // 微小文字
```

### 圆角变量
```less
@common_border_radius_s  // 小圆角 (4px)
@common_border_radius_m  // 中圆角 (6px)
@common_border_radius_l  // 大圆角 (8px)
```

### 阴影 Mixin
```less
.common_box_shadow_s()  // 小阴影
.common_box_shadow_m()  // 中阴影
.common_box_shadow_l()  // 大阴影
```

### 动画变量
```less
@common_light_motion_duration           // 轻动画时长 (0.2s)
@common_normal_motion_duration          // 正常动画时长 (0.24s)
@common_hard_motion_duration            // 重动画时长 (0.36s)
@common_light_motion_timing_function    // 轻动画缓动函数
@common_normal_motion_timing_function   // 正常动画缓动函数
@common_hard_motion_timing_function     // 重动画缓动函数
```
