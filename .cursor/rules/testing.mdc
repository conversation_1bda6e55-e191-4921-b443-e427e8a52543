---
globs: **/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx
---

# 测试开发规范

## 测试工具
- 使用 `@ali/dtest-toolbox` 进行测试
- 钉钉测试工具链支持

## 测试文件命名
- 单元测试: `Component.test.tsx`
- 集成测试: `Component.spec.tsx`
- 测试文件与源文件同目录

## 测试结构

### 组件测试
```typescript
import React from 'react';
import { render, screen } from '@testing-library/react';
import Component from './Component';

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    render(<Component onClick={handleClick} />);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
});
```

### API 测试
```typescript
import { getProductList } from './productApi';

describe('productApi', () => {
  it('should fetch product list', async () => {
    const mockData = { data: [], total: 0 };

    // Mock API 响应
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    });

    const result = await getProductList();
    expect(result).toEqual(mockData);
  });
});
```

## 测试最佳实践

### 测试覆盖率
- 组件测试覆盖主要功能
- API 测试覆盖成功和失败场景
- 工具函数测试覆盖边界情况

### 模拟数据
- 使用 `src/data/` 目录中的模拟数据
- 保持测试数据的一致性
- 测试环境与开发环境数据分离

### 国际化测试
- 测试多语言场景
- 验证文本翻译正确性
- 测试不同语言的布局

### 钉钉环境测试
- 模拟钉钉 JSAPI 调用
- 测试移动端适配
- 验证权限和认证流程

## 测试命令
```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- Component.test.tsx

# 生成测试覆盖率报告
npm run test:coverage
```

## 持续集成
- 使用钉钉 Aone Hook 进行代码检查
- 测试必须通过才能合并代码
- 自动化测试流程集成
