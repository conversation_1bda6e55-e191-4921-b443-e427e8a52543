---
globs: *.ts,*.tsx
---

# TypeScript 和 React 开发规范

## TypeScript 规范

### 类型定义
- 所有类型定义放在 [src/common/types/index.ts](mdc:src/common/types/index.ts) 或对应的 types 目录中
- 使用 interface 定义对象类型，使用 type 定义联合类型和别名
- 导出的接口使用 `I` 前缀，枚举使用 `E` 前缀

### 导入规范
```typescript
// 使用路径别名
import { Product } from '@/common/types';
import { getProductList } from '@/apis/production';

// 组件导入
import { Button } from 'antd';
import { FC } from 'react';
```

## React 组件规范

### 组件定义
```typescript
interface ComponentProps {
  // 定义 props 类型
}

const Component: FC<ComponentProps> = ({ ...props }) => {
  // 组件逻辑
  return <div>...</div>;
};

export default Component;
```

### 状态管理
- 使用 Recoil 进行全局状态管理
- 本地状态使用 useState 和 useEffect hooks
- 复杂状态逻辑考虑使用 useReducer

### 样式规范
- 每个组件对应一个 .less 文件
- 支持 CSS Modules 和 Tailwind CSS
- 使用 Ant Design 组件优先

## 页面组件规范

### 入口文件 (app.tsx)
```typescript
import React from 'react';
import { render } from 'react-dom';
import PageComponent from './index';

render(<PageComponent />, document.getElementById('root'));
```

### 主组件 (index.tsx)
- 负责页面的主要逻辑和数据获取
- 使用 useEffect 处理页面初始化
- 集成国际化和钉钉 JSAPI

## 最佳实践
- 使用 TypeScript 严格模式
- 组件要有明确的 props 类型定义
- 使用 React.memo 优化性能
- 错误边界处理
- 支持国际化 (i18n)
