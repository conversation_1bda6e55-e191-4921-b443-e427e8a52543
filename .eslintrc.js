module.exports = {
  extends: [
    'ali/typescript/react'
  ],
  parserOptions: {
    project: './tsconfig.json',
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  rules: {
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
    'react/jsx-no-bind': 0,
    'react/no-danger': 0,
    'react-hooks/exhaustive-deps': 0,
    indent: [2, 2, { SwitchCase: 1 }],
  },
};
