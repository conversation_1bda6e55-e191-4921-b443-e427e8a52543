#### 启动项目

```javascript
tnpm install
tnpm start
```

#### 打包构建

```javascript
tnpm run build
```

#### 工程目录

```html
├── i18nResource // 国际化配置
├── scripts // npm script 相关启动方法
├── src
│ ├── __tests__ // 单元测试目录
│ ├── assets // 基础资源, 如 iconfont、公共 css
│ ├── components // 项目级公共组件
│ ├── containers // 项目级公共模块
│ ├── interfaces // typescript 定义
│ ├── apis // 接口请求
│ ├── common
│ │ ├── styles // 全局样式
│ │ └── ts // 全局 ts
│ ├── pages
│ │ ├── demo // 多页应用示例
│ │ │ ├── components // 页面级组件
│ │ │ ├── containers // 页面级模块
│ │ │ └── index.tpl // 页面 html
│ │ │ └── init.ts // 页面初始化
│ │ └── spa-demo // 单页应用实例 (无状态管理库)
│ │ │ ├── components // 应用级组件
│ │ │ ├── containers // 应用级模块
│ │ │ ├── index.tpl // 页面 html
│ │ │ └── init.ts // 页面初始化
│ │ │ └── views // 单页面
│ │ │ │ └── index // 单页面
│ │ │ │ │ ├── components // 页面级组件
│ │ │ │ │ └── containers // 页面级模块
│ │ │ │ └── tools // 页面级基础工具函数
│ │ └── spa-demo-use-recoil // 单页应用示例（使用了recoil作为状态管理）
│ │ │ ├── components // 应用级组件
│ │ │ ├── containers // 应用级模块
│ │ │ ├── index.tpl // 页面 html
│ │ │ └── init.ts // 页面初始化
│ │ │ └── views // 单页面
│ │ │ │ └── index // 单页面
│ │ │ │ │ ├── components // 页面级组件
│ │ │ │ │ └── containers // 页面级模块
│ │ │ │ ├── store // recoil 配置, 例如 atom.ts
│ │ │ │ └── tools // 页面级基础工具函数
│ └── utils // 项目级基础工具函数
├── types
├── webpack
│ ├── config
│ │ ├── webpack.base.config.js // 基础配置, 包含 babel、plugins 等
│ │ ├── webpack.dev.config.js // 开发环境配置, 如 devServer、mock
│ │ └── webpack.prod.config.js // 打包配置, 主要用于读取 DEF 构建环境
│ └── utils
│ ├── getPublicPath.js // 获取 DEF 构建 publicPath
│ ├── rootPath.js // 获取目前相对路径
│ └── pageList.js // 工程页面列表
├── .babelrc
├── .eslintrc.js // eslint 配置
├── .stylelintrc.js // stylelint 配置
├── yarn.lock
└── package.json
```

#### 离线包接入完善
目前代码模板中的localresource.json中进行了初步的离线包配置，需要根据实际进行完善，比如替换连接中${projectName}变量，具体可参考离线包接入指南：<a href="https://alidocs.dingtalk.com/i/nodes/jQPRqwxd3NLWjd4bMDKdJYK6lrGM4795" target="_blank">https://alidocs.dingtalk.com/i/nodes/jQPRqwxd3NLWjd4bMDKdJYK6lrGM4795</a>。
如果不需要使用离线包，把abc.json中assets.command.cmd数组的最后一项删除即可。

#### ding-design组件库
文档地址: <a href="https://dd.alibaba-inc.com/#/" target="_blank">https://dd.alibaba-inc.com/#/</a>

#### 钉钉统一JSAPI
文档地址: <a href="https://open-center.alibaba-inc.com/ability/jsapi/dashboard" target="_blank">https://open-center.alibaba-inc.com/ability/jsapi/dashboard</a>

#### 钉钉统一工具函数
文档地址: <a href="https://pds.alibaba-inc.com/SWH5/sw-utils/function/index.html#static-function-ut" target="_blank">https://pds.alibaba-inc.com/SWH5/sw-utils/function/index.html#static-function-ut</a>

#### 脚手架使用文档
文档地址(复制后使用钉钉打开): <a href="https://notes.dingtalk.com/doc/nb9XJxdKpbxKPGyA?orgId=21001&dd_progress=false&showmenu=false" target="_blank">https://notes.dingtalk.com/doc/nb9XJxdKpbxKPGyA?orgId=21001&dd_progress=false&showmenu=false</a>

#### 通用快照接入
文档地址: <a href="https://anpm.alibaba-inc.com/package/@ali/snapshot-dd-webpack-plugin/" target="_blank">https://anpm.alibaba-inc.com/package/@ali/snapshot-dd-webpack-plugin/</a>

#### 离线包接入指南
文档地址: <a href="https://alidocs.dingtalk.com/i/nodes/jQPRqwxd3NLWjd4bMDKdJYK6lrGM4795" target="_blank">https://alidocs.dingtalk.com/i/nodes/jQPRqwxd3NLWjd4bMDKdJYK6lrGM4795</a>

#### SSG接入指南
文档地址: <a href="https://alidocs.dingtalk.com/i/nodes/ydxXB52LJqPvwoK0IwE2mRa0VqjMp697?utm_scene=team_space&utm_medium=dingdoc_doc_plugin_card&utm_source=dingdoc_doc" target="_blank">https://alidocs.dingtalk.com/i/nodes/ydxXB52LJqPvwoK0IwE2mRa0VqjMp697?utm_scene=team_space&utm_medium=dingdoc_doc_plugin_card&utm_source=dingdoc_doc</a>

#### 单元测试
文档地址: <a href="https://yuque.antfin.com/docs/share/2124da9b-87dd-42bf-9bf1-6b725af7ceff#RcuZ3" target="_blank">https://yuque.antfin.com/docs/share/2124da9b-87dd-42bf-9bf1-6b725af7ceff#RcuZ3</a>

#### 开启热更新
由于热更新会对output library模式的输出造成影响，为了避免同学遇到类似问题找不到问题源头，默认关闭了热更新。  
如需开启，只需两步操作：  
1、devServer配置中，设置injectClient、hot属性
```javascript
{
  ...,
  devServer: {
    injectClient: true, // 把这行代码删掉 or 设置为true
    hot: true, // hot设置为true
  },
  ...
}
```  
  
2、webpack.dev.config.js配置中，增加target: 'web'属性
```javascript
{
  ...,
  target: 'web',
  ...
}
```

#### commit-msg 的 git hook
1、每次提交commit，会按照"to #${aone_id}, ${commit_message}"的格式校验commit信息是否合法  
2、如果不需要，可以在package.json里移除@ali/dingtalk-aone-hook。删除node_modules再重新tnpm install即可