#!/bin/bash

function eslintcode () {
    echo "$(tput setaf 2)开始执行 ESLint 检查 ... $(tput sgr 0)"
    file=$({ git diff --name-only --staged; } | sort | uniq | grep -E '\.(js|jsx)$')
    # 执行两次 ESLint 是为了获得原始带色彩的输出
    # 现在只检查有变化的文件，所以对速度没什么影响
    if [[ $file != "" ]]; then
        ./node_modules/eslint/bin/eslint.js $file
        result=$(./node_modules/eslint/bin/eslint.js $file);
        if [[ $result != "" && $result != *"(0 error"* ]]; then
            echo "$(tput setaf 1)ESLint 检查未通过$(tput sgr0)"
            exit 1 # exit with failure status
        fi
    fi
    echo "$(tput setaf 2)ESLint 检查通过 $(tput sgr 0)"
}

eslintcode