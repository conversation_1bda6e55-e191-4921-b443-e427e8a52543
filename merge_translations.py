#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import re
import os

# 定义文件路径
zh_file = './src/i18n/translation_zh_CN.js'
en_file = './src/i18n/translation_en_US.js'
jp_file = './src/i18n/translation_ja_JP.js'
output_file = './translations.xlsx'

# 提取JS对象为Python字典的函数
def extract_js_object(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取键值对
    pattern = r'"([^"]+)":\s*"([^"]*(?:\\.[^"]*)*)",?'
    matches = re.findall(pattern, content)
    
    # 创建字典
    result = {}
    for key, value in matches:
        # 处理转义字符
        value = value.replace('\\"', '"').replace('\\n', '\n')
        result[key] = value
    
    return result

# 提取所有翻译文件的内容
try:
    zh_data = extract_js_object(zh_file)
    en_data = extract_js_object(en_file)
    jp_data = extract_js_object(jp_file)
    
    print(f"中文翻译条目数: {len(zh_data)}")
    print(f"英文翻译条目数: {len(en_data)}")
    print(f"日文翻译条目数: {len(jp_data)}")
    
    # 获取所有唯一的键
    all_keys = set(zh_data.keys()) | set(en_data.keys()) | set(jp_data.keys())
    print(f"总共唯一键数: {len(all_keys)}")
    
    # 创建数据框
    data = []
    for key in sorted(all_keys):
        row = {
            'AppName': 'j-dingtalk-web',
            'Key': key,
            'Simplified Chinese': zh_data.get(key, ''),
            'English': en_data.get(key, ''),
            'Japanese': jp_data.get(key, '')
        }
        data.append(row)
    
    # 创建DataFrame并保存为Excel
    df = pd.DataFrame(data)
    
    # 设置列的顺序
    df = df[['AppName', 'Key', 'Simplified Chinese', 'English', 'Japanese']]
    
    # 保存到Excel文件
    df.to_excel(output_file, index=False)
    
    print(f"翻译已成功合并到 {os.path.abspath(output_file)}")
    
except Exception as e:
    print(f"处理过程中出错: {e}")
