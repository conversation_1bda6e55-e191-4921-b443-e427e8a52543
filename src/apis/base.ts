import lwp$ from '@ali/dingtalk-jsapi/api/internal/request/lwp';

export default async function request<T = any>(uri, body: any[]): Promise<T> {
  const resposne = await lwp$({
    uri,
    body,
    headers: {},
  });
  const responseOK = resposne.code === 200;

  console.log('request', uri, body, resposne);

  if (responseOK) {
    return resposne.body as T;
  }

  throw new Error(resposne.body?.reason);
}
