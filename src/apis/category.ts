import request from './base';

// 获取类目列表
export const getCategoryList = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getCategoryList', [data]);
};

// 获取发送消息的参数
export const getMessageParames = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/generateNewMsg', [data]);
};

// 发送消息给助理
export const sendCopilotMsgByUser = (data: [] = []) => {
  return request('/r/Adaptor/AICopilotI/sendCopilotMsgByUser', [...data]);
};

// 订阅类目
export const subscribeCategory = (data: {}) => {
  return request('/r/Adaptor/ProductRpcI/subscribeCategory', [data]);
};

// 选择类目
export const selectCardCategoryPath = (data: {}) => {
  return request('/r/Adaptor/ProductRpcI/selectCardCategoryPath', [data]);
};

// 获取类目路径
export const queryCategoryPath = (data: {}) => {
  return request('/r/Adaptor/ProductRpcI/queryCategoryPath', [data]);
};

// 保存类目
export const switchReport = (data: {}) => {
  return request('/r/Adaptor/ProductRpcI/switchReport', [data]);
};
