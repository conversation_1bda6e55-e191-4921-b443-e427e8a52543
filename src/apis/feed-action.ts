import request from './base';

// ==================== 类型定义 ====================

// 点赞业务请求入参
export interface RecFeedLikeModel {
  tenantId: string; // 租户 ID
  corpId: string; // 企业 ID
  itemId: string; // feed ID
}

// 点赞业务结果
export interface RecFeedLikeResult {
  success: boolean; // 是否点赞成功
}

// 推荐业务请求入参
export interface RecFeedRecommendModel {
  tenantId: string; // 租户 ID
  corpId: string; // 企业 ID
  itemId: string; // feed ID
}

// 推荐业务结果
export interface RecFeedRecommendResult {
  success: boolean; // 是否推荐成功
}

// 查询 Feed 上用户推荐的情况请求入参
export interface RecFeedUserRecommendQueryModel {
  tenantId: string; // 租户 ID
  corpId: string; // 企业 ID
  itemId: string; // feed ID
  cursor: string; // 游标
  size: number; // 一批拉取数量
  excludeUids: number[]; // 排除的uid列表
}

// 推荐用户信息
export interface RecFeedRecommendUserInfo {
  uid: number; // 用户uid
  nick: string; // 用户名称
  avatarMediaId: string; // 用户头像mediaId
}

// 查询 Feed 上用户推荐的情况结果
export interface RecFeedUserRecommendResult {
  recFeedRecommendUserInfos: RecFeedRecommendUserInfo[]; // 推荐用户列表
  cursor: string; // 下一次查询用游标
  hasMore: boolean; // 是否有更多数据
}

// 查询用户推荐列表请求入参
export interface RecUserRecommendQueryModel {
  tenantId: string; // 租户 ID
  cursor: string; // 游标
  size: number; // 本次拉取条数
}

// 查询用户点赞列表请求入参
export interface RecUserLikeFeedQueryModel {
  tenantId: string; // 租户 ID
  cursor: string; // 游标
  size: number; // 本次拉取条数
}

// Feed 详情结构
export interface RecFeedDetail {
  itemId: string; // 物品ID
  itemName: string; // 物品名称
  itemType: string; // 内容类型
  createId: string; // 创建者ID
  priority: number; // 优先级
  categoryId: string; // 类别ID
  source: string; // 来源
  tag: string; // 标签
  expireTimestamp: number; // 过期时间戳
  coverImg: string; // 封面图片MediaId
  payload: string; // 内容数据（JSON格式）
  qaContext: string; // 问答QA上下文
  interactionContext: string; // 互动上下文
  statistics: RecFeedItemStatistics; // 统计信息
  extra: string; // 扩展数据（JSON格式）
}

// Feed 统计信息
export interface RecFeedItemStatistics {
  likeCnt: number; // 点赞数
  recommendCnt: number; // 推荐数
  favoriteCnt: number; // 收藏数
  commentCnt: number; // 评论数
  like: boolean; // 当前用户是否点赞
  recommend: boolean; // 当前用户是否推荐
  favorite: boolean; // 当前用户是否收藏
  userRecommends: RecFeedItemUserRecommend[]; // 用户推荐信息
  browseTimestamp: number; // 最近一次浏览时间戳
}

// Feed 用户推荐信息
export interface RecFeedItemUserRecommend {
  userId: number; // 用户ID
  nick: string; // 用户昵称
  avatarMediaId: string; // 用户头像MediaId
}

// 查询用户推荐列表结果
export interface RecUserRecommendQueryResult {
  feeds: RecFeedDetail[]; // 列表数据
  cursor: string; // 下一次查询用游标
  hasMore: boolean; // 是否有更多数据
}

// 查询用户点赞列表结果
export interface RecUserLikeFeedQueryResult {
  feeds: RecFeedDetail[]; // 列表数据
  cursor: string; // 下一次查询用游标
  hasMore: boolean; // 是否有更多数据
}

// 收藏业务请求入参
export interface RecFeedCollectModel {
  tenantId: string; // 租户 ID
  corpId: string; // 企业 ID
  itemId: string; // 动态 ID
}

// 收藏业务结果
export interface RecFeedCollectResult {
  success: boolean; // 是否收藏成功
}

// 查询用户收藏列表请求入参
export interface RecUserCollectQueryModel {
  tenantId: string; // 租户 ID
  cursor: string; // 游标
  size: number; // 本次拉取条数
}

// 查询用户收藏列表结果
export interface RecUserCollectQueryResult {
  feeds: RecFeedDetail[]; // 列表数据
  cursor: string; // 下一次查询用游标
  hasMore: boolean; // 是否有更多数据
}

// 删除评论请求模型
export interface RecFeedCommentDeleteRequestModel {
  tenantId: string; // 租户 ID
  itemId: string; // feed ID
  commentId: string; // 评论 ID
}

// 删除评论结果模型
export interface RecFeedCommentDeleteResultModel {
  success: boolean; // 是否删除成功
  deleteCommentId: string; // 删除的评论 ID
}

// 查询浏览历史请求参数
export interface RecQueryBrowseHistoryRequest {
  tenantId: string; // 租户id
  cursor?: string; // 下一页游标
  size: number; // 每页数量
}

// 查询浏览历史响应
export interface RecQueryBrowseHistoryResponse {
  items: RecFeedDetail[]; // 历史记录项列表
  cursor: string; // 下一页游标
  size: number; // 每页数量
}

// 查询用户评论列表请求参数
export interface RecFeedUserCommentListRequestModel {
  cursor?: string; // 游标
  size: number; // 每页数量
}

// 用户评论列表结果
export interface RecFeedUserCommentListResultModel {
  userId: number; // 用户id
  commentInfoList: RecUserCommentModel[]; // 评论列表
  cursor: string; // 新游标
  hasMore: boolean; // 是否有更多
}

// 用户评论模型
export interface RecUserCommentModel {
  uid: number; // 评论人UID
  productTitle: string; // 商品标题
  imageUrl: string; // 商品主图
  commentId: number; // 当前评论ID
  comment: RecCommentContentModel; // 评论内容
  commentTimestamp: number; // 评论时间戳
  jumpUrl: string; // 商品跳转链接
  videoUrl: string; // 视频跳转链接
}

// 评论内容模型
export interface RecCommentContentModel {
  type: string; // 内容类型
  content: string; // 具体内容
}

// ==================== API 方法 ====================

/**
 * 对单条 Feed 进行推荐
 * @param recFeedRecommendModel 推荐请求模型
 * @returns 推荐结果
 */
export const recommendFeed = (recFeedRecommendModel: RecFeedRecommendModel): Promise<RecFeedRecommendResult> => {
  return request('/r/Adaptor/RecommendFeedI/recommendFeed', [recFeedRecommendModel]);
};

/**
 * 对单条 Feed 取消推荐
 * @param recFeedRecommendModel 取消推荐请求模型
 * @returns 取消推荐结果
 */
export const unRecommendFeed = (recFeedRecommendModel: RecFeedRecommendModel): Promise<RecFeedRecommendResult> => {
  return request('/r/Adaptor/RecommendFeedI/unRecommendFeed', [recFeedRecommendModel]);
};

/**
 * 查询 Feed 上用户推荐的情况
 * @param recFeedUserRecommendQueryModel 推荐请求模型
 * @returns 推荐结果
 */
export const queryFeedUserRecommendList = (recFeedUserRecommendQueryModel: RecFeedUserRecommendQueryModel): Promise<RecFeedUserRecommendResult> => {
  return request('/r/Adaptor/RecommendFeedI/queryFeedUserRecommendList', [recFeedUserRecommendQueryModel]);
};

/**
 * 查询用户点过推荐的Feed列表
 * @param recUserRecommendQueryModel 查询请求模型
 * @returns 查询结果
 */
export const queryUserRecommendFeedList = (recUserRecommendQueryModel: RecUserRecommendQueryModel): Promise<RecUserRecommendQueryResult> => {
  return request('/r/Adaptor/RecommendFeedI/queryUserRecommendFeedList', [recUserRecommendQueryModel]);
};

/**
 * 对单条 Feed 进行收藏
 * @param recFeedCollectModel 收藏请求模型
 * @returns 收藏结果
 */
export const collectFeed = (recFeedCollectModel: RecFeedCollectModel): Promise<RecFeedCollectResult> => {
  return request('/r/Adaptor/RecommendFeedI/collectFeed', [recFeedCollectModel]);
};

/**
 * 对单条 Feed 取消收藏
 * @param recFeedCollectModel 取消收藏请求模型
 * @returns 取消收藏结果
 */
export const unCollectFeed = (recFeedCollectModel: RecFeedCollectModel): Promise<RecFeedCollectResult> => {
  return request('/r/Adaptor/RecommendFeedI/unCollectFeed', [recFeedCollectModel]);
};

/**
 * 查询用户收藏Feed列表
 * @param recUserCollectQueryModel 查询请求模型
 * @returns 查询结果
 */
export const queryUserCollectedFeedList = (recUserCollectQueryModel: RecUserCollectQueryModel): Promise<RecUserCollectQueryResult> => {
  return request('/r/Adaptor/RecommendFeedI/queryUserCollectedFeedList', [recUserCollectQueryModel]);
};

/**
 * 点赞Feed
 * @param recFeedLikeModel 点赞请求参数
 * @returns 返回点赞结果
 */
export const likeFeed = (recFeedLikeModel: RecFeedLikeModel): Promise<RecFeedLikeResult> => {
  return request('/r/Adaptor/RecommendFeedI/likeFeed', [recFeedLikeModel]);
};

/**
 * 取消点赞Feed
 * @param recFeedLikeModel 取消点赞请求参数
 * @returns 返回取消点赞结果
 */
export const cancelLikeFeed = (recFeedLikeModel: RecFeedLikeModel): Promise<RecFeedLikeResult> => {
  return request('/r/Adaptor/RecommendFeedI/cancelLikeFeed', [recFeedLikeModel]);
};

/**
 * 查询用户点过赞的Feed列表
 * @param recUserLikeFeedQueryModel 查询请求参数
 * @returns 返回用户点过喜欢的Feed列表
 */
export const queryUserLikeFeedList = (recUserLikeFeedQueryModel: RecUserLikeFeedQueryModel): Promise<RecUserLikeFeedQueryResult> => {
  return request('/r/Adaptor/RecommendFeedI/queryUserLikeFeedList', [recUserLikeFeedQueryModel]);
};

/**
 * 删除Feed的评论
 * 如果是根评论，会同时删除其所有子评论
 * 删除后会更新Feed的评论统计数
 * @param model 删除评论请求模型
 * @returns 删除评论结果
 */
export const deleteFeedComment = (model: RecFeedCommentDeleteRequestModel): Promise<RecFeedCommentDeleteResultModel> => {
  return request('/r/Adaptor/RecommendFeedI/deleteFeedComment', [model]);
};

/**
 * 查询历史浏览记录
 * @param request 请求参数，包含租户ID、游标和返回数量
 * @returns 返回历史浏览记录响应
 */
export const queryBrowseHistory = (requestParam: RecQueryBrowseHistoryRequest): Promise<RecQueryBrowseHistoryResponse> => {
  return request('/r/Adaptor/RecommendFeedI/queryBrowseHistory', [requestParam]);
};

/**
 * 查询我发出的评论
 * @param request 查询用户评论列表请求参数
 * @returns 评论信息列表
 */
export const selectCommentByUid = (requestParam: RecFeedUserCommentListRequestModel): Promise<RecFeedUserCommentListResultModel> => {
  return request('/r/Adaptor/UserRecommendI/queryCommentByUid', [requestParam]);
};
