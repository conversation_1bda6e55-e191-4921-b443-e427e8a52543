import request from './base';

// 获取选品商品列表
export const getProductList = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getProductList', [data]);
};

// 获取选品报告详情
export const getProductSelectionReportDetail = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getProductSelectionReportDetail', [data]);
};

// 获取商品评论列表
export const getProductComment = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getProductComment', [data]);
};

// 获取商品列表-1vN
export const getProductSelectionDetail = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getProductSelectionDetail', [data]);
};

// 图片搜索
export const queryImageSearch = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/queryImageSearch', [data]);
};

// 好店好号详情页查询接口
export const queryGoodShoppExpertDetail = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/queryGoodShoppExpertDetail', [data]);
};

// 举报假冒伪劣
export const reportFakeProduct = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/sourcingProductReport', [data]);
};

// 商品审核
export const productAudit = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/audit', [data]);
};
