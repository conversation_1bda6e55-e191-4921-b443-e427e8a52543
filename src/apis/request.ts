// Import axios and types
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Base request function with common logic
 * @param {string} endpoint - API endpoint path
 * @param {string} method - HTTP method
 * @param {any} data - Request data
 * @param {Record<string, string>} headers - Request headers
 * @returns {Promise<AxiosResponse>} - Axios response promise
 */
function baseRequest(
  endpoint: string,
  method = 'GET',
  data: any = {},
  headers: Record<string, string> = {},
): Promise<AxiosResponse> {
  const baseUrl = '/v1/api';
  let url = `${baseUrl}${endpoint}`;

  const config: AxiosRequestConfig = {
    url,
    method,
    headers,
  };

  // Handle data based on method type
  if (method.toUpperCase() === 'GET') {
    // For GET requests, append parameters to URL
    if (data && Object.keys(data).length > 0) {
      const searchParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
        config.url = url;
      }
    }
  }

  // For non-GET requests, use request body
  if (method.toUpperCase() !== 'GET' && data) {
    config.data = data;
  }

  return axios(config);
}

// 图片搜索
export const queryProductByImage = (data: Record<string, any> = {}) => {
  return baseRequest('/sourcing', 'GET', data);
};

// 举报假冒伪劣
export const reportProduct = (data: Record<string, any> = {}) => {
  return baseRequest('/sourcing/product/report', 'POST', data);
};

// Export the base request function for custom usage
export { baseRequest };
