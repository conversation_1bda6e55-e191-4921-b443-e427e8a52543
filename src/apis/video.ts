import request from './base';

// 生成视频
export const generateVideo = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateVideo', [data]);
};

// 查看视频生成状态
export const checkVideoStatus = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/checkVideoStatus', [data]);
};

// 生成GIF
export const generateGif = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateGif', [data]);
};

// 轮询GIF的生成结果
export const checkGifStatus = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/checkGifStatus', [data]);
};

// 重新生成视频
export const reGenerateVideo = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateVideo', [data]);
};

// 获取视频列表
export const listVideos = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/listVideos', [data]);
};

// 评价视频
export const rateVideo = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/rateVideo', [data]);
};

// 上传图片
export const uploadImage = (data: {} = {}) => {
  return request('/r/Adaptor/FileRpcI/upload', [data]);
};

// 删除视频
export const removeVideo = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/removeVideo', [data]);
};
