// 定义产品数据类型
export interface Product {
  [x: string]: any;
  productId: number;
  images?: string[];
  tagArr?: string[];
  storeName: string;
  detailHtml?: string;
  title: string;
  price: string;
  originalPrice: string;
  profitRate: string;
  monthlySales: string;
  repeatRate: string;
  commentReport?: any;
}

export interface IUTParams {
  platform?: string | Record<string, string>;
  reportType?: string | Record<string, string>;
  bizId?: string | Record<string, string>;
  rankType?: string | Record<string, string>;
  productId?: string | Record<string, string>;
  productTitle?: string | Record<string, string>;
}

export interface IOriginProduct {
  productId?: string;
  title?: string;
  titleTranslated?: string;
  primaryImage?: string;
  detailHtml?: string;
  priceInfo?: {
    price: string;
  };
  auditCode: number; // 1: 通过 / -1: 不通过 / 0: 未审核
  auditStatus: string; // 通过 /不通过 /未审核
  aiAuditStatus: number; // 1: 已审核商品 / 0: 未审核商品，标识当前商品是否被人审核过寻源商品
}

export interface IProductData {
  productId: string;
  image: string;
  title: string;
  titleTranslated: string;
  storeName?: string;
  price: string;
  priceUnit: string;
  favNum: number;
  reviewAverageScore: number;
  platform?: EPlatform;
  reviewable: boolean; // 是否可被审核，根据用户权限会返回true，没权限没有该字段。该字段控制寻源页是否出现审核的按钮等
  auditStatus: number; // 1: 已审核商品 / 0: 未审核商品，标识当前商品是否被人审核过寻源商品
}

export interface ReviewItemData {
  feature: string;
  percentage: number;
  count: string;
  trend: number;
}

export interface RateItemWithPercentProps {
  item: ReviewItemData;
}

export interface ZozoCommentsProps {
  showTitle?: boolean;
  setDrawerVisible?: (visible: boolean, productId?: number, sourceItemId?: number) => void;
  data: any;
  pageType?: string;
  platform?: EPlatform;
}

export interface AmazonCommentsProps {
  showMore: boolean;
  setDrawerVisible?: (visible: boolean, productId?: number, sourceItemId?: number) => void;
  data: any;
  pageType?: string;
  platform?: EPlatform;
}

export interface CommentItemProps {
  item: ReviewItemData;
}

// 评论
export interface IComment {
  id: number;
  likes: boolean;
  start: number;
  avatar: string;
  content: string;
  date: number;
  name: string;
  time: number;
  title: string;
}

export interface IProduct {
  selected: boolean;
  productId: string;
  title: string;
  primaryImage: string;
  salePrice: number;
  score: number;
  platform?: string;
  detailHtml?: string;
}

export enum EPlatform {
  ZOZOTOWN = 'zozotown',
  AMAZON = 'amazon',
  REDKOL = 'red_kol',
  TIKTOK = 'dy_kol',
  TAOBAO = 'taobao_shop',
  ALIBABA = 'ali1688_shop',
  RAKUTEN = 'rakuten',
  MANUAL = 'manual',
}

// 频率类型
export type FrequencyType = 'daily' | 'weekly' | 'monthly';

// 日期类型
export type DayType = 'workday' | 'everyday' | 'friday' | 'first';

// 订阅频率配置
export interface FrequencyConfig {
  type: FrequencyType;
  dayType: DayType;
  time: string;
  dataTypeIndex?: number;
  timeIndex?: number;
}

// Feed Payload 结构定义
export interface IFeedPayload {
  creator: {
    nickName: string;
    id: string;
    avatar: string;
  };
  goods: {
    intro: string;
    item1688Id: string;
    name: string;
    image: string;
    jumpUrl: string;
    price1688: number;
    priceJapan: number;
    grossMargin: string;
    status: string;
    bizType: string;
    reviewStatus: string;
    richIntro: string;
    agentCode: string;
    assistantIcon: string;
    assistantName: string;
    assistantLuiLink: string;
    assistantUid: number;
    corpId: string;
    category: string;
    categoryName: string;
    rankerTypes: string[];
    features: Record<string, string>;
  };
  video?: {
    url: string;
    imageUrl: string;
  };
}
