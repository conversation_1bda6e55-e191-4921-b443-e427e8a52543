.image-swiper-container {
  position: relative;
  width: 100%;
  background-color: var(--common_level7_base_color);
  border-radius: var(--common_border_radius_l);
  overflow: hidden;
  
  .image-swiper-content {
    width: 100%;
    font-size: 0;
    aspect-ratio: 1/1; // 固定宽高比防止高度坍塌
    position: relative;
    cursor: pointer;
    background-color: var(--common_level7_base_color);

    // 触摸相关样式
    touch-action: pan-y;
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    
    .optimized-image-container {
      width: 100%;
      height: 100%;
      
      .swiper-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }
    }
    
    // 点击反馈效果
    &:active {
      opacity: 0.9;
      transform: scale(0.98);
      transition: all var(--common_light_motion_duration) var(--common_light_motion_timing_function);
    }
  }
  
  // Swiper 指示器样式优化
  .dtm-swiper-indicator {
    bottom: 12px;
    
    .dtm-swiper-indicator-dot {
      background-color: var(--common_level5_base_color);
      
      &.dtm-swiper-indicator-dot-active {
        background-color: var(--common_level1_base_color);
      }
    }
  }
  
  // 单张图片时隐藏指示器
  &.single-image {
    .dtm-swiper-indicator {
      display: none;
    }
  }

  .image-swiper-tag {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px;
    border-radius: 190px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    cursor: pointer;
    .image-swiper-tag-text {
      font-size: 12px;
      line-height: 16px;
      color: #FFFFFF;
    }
    .image-swiper-tag-icon {
      font-size: 12px;
      height: 15px;
      color: #FFFFFF;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .image-swiper-container {
    .image-swiper-content {
      // 在小屏幕上稍微调整宽高比
      aspect-ratio: 1/1;
    }
  }
}