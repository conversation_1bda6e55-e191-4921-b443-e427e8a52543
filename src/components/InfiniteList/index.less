@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.infinite-list-page {
  min-height: 100vh;
  background-color: @common_bg_z0_color;
}

.infinite-list-content {
}

.infinite-list-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  color: @common_level3_base_color;
  padding: 20px;
}

.infinite-list-no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: @common_level4_base_color;
  .common_footnote_text_style_mob();
  span {
    position: relative;
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 40px;
      height: 1px;
      background-color: @common_line_light_color;
    }
    &::before {
      left: -50px;
    }
    &::after {
      right: -50px;
    }
  }
}

.infinite-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: @common_level4_base_color;
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  .empty-text {
    .common_body_text_style_mob();
  }
}

.infinite-list-waterfall-container,
.infinite-list-grid-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  padding: 8px;
}

.infinite-list-waterfall-column,
.infinite-list-grid-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  gap: 8px;
}

.infinite-list-list-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
}

@media (max-width: 768px) {
  .infinite-list-waterfall-container,
  .infinite-list-grid-container {
    gap: 8px;
    .infinite-list-waterfall-column,
    .infinite-list-grid-column {
      gap: 8px;
    }
  }
}
