import React, { useEffect, useRef } from 'react';
import { Spin } from 'dingtalk-design-mobile';
import { HeartOutlined } from '@ant-design/icons';
import './index.less';

export type InfiniteListLayout = 'waterfall' | 'list' | 'grid';

interface InfiniteListProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loading?: boolean;
  error?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  layout?: InfiniteListLayout;
  columnCount?: number; // 仅 waterfall/grid 有效
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  errorText?: string;
  errorIcon?: React.ReactNode;
  noMoreText?: string;
  className?: string;
  style?: React.CSSProperties;
}

function getDefaultEmptyIcon() {
  return <HeartOutlined className="empty-icon" />;
}
function getDefaultErrorIcon() {
  return <span className="empty-icon">⚠️</span>;
}

function getDefaultNoMoreText() {
  return <span>没有更多数据了</span>;
}

function getDefaultEmptyText() {
  return '暂无数据';
}
function getDefaultErrorText() {
  return '网络错误，请稍后重试';
}

function splitColumns<T>(data: T[], columnCount: number): T[][] {
  const cols: T[][] = Array.from({ length: columnCount }, () => []);
  data.forEach((item, idx) => {
    cols[idx % columnCount].push(item);
  });
  return cols;
}

export function InfiniteList<T>({
  data,
  renderItem,
  loading,
  error,
  hasMore,
  onLoadMore,
  layout = 'list',
  columnCount = 2,
  emptyText,
  emptyIcon,
  errorText,
  errorIcon,
  noMoreText,
  className = '',
  style,
}: InfiniteListProps<T>) {
  const scrollRef = useRef<HTMLDivElement>(null);

  // 滚动触底加载
  useEffect(() => {
    if (!onLoadMore || loading || !hasMore) return;
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      if (scrollTop + windowHeight >= documentHeight - 100) {
        onLoadMore();
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [onLoadMore, loading, hasMore]);

  // 错误页
  if (error) {
    return (
      <div className={`infinite-list-page ${className}`} style={style}>
        <div className="infinite-list-content">
          <div className="infinite-list-empty">
            {errorIcon || getDefaultErrorIcon()}
            <div className="empty-text">{errorText || getDefaultErrorText()}</div>
          </div>
        </div>
      </div>
    );
  }

  // 空状态
  if (data.length === 0 && !loading) {
    return (
      <div className={`infinite-list-page ${className}`} style={style}>
        <div className="infinite-list-content">
          <div className="infinite-list-empty">
            {emptyIcon || getDefaultEmptyIcon()}
            <div className="empty-text">{emptyText || getDefaultEmptyText()}</div>
          </div>
        </div>
      </div>
    );
  }

  // 列表布局
  let listBody: React.ReactNode = null;
  if (layout === 'waterfall' || layout === 'grid') {
    const columns = splitColumns(data, columnCount);
    listBody = (
      <div className={`infinite-list-${layout}-container`}>
        {columns.map((col, idx) => (
          <div key={idx} className={`infinite-list-${layout}-column`}>
            {col.map((item, i) => renderItem(item, i))}
          </div>
        ))}
      </div>
    );
  } else {
    // 普通列表
    listBody = (
      <div className="infinite-list-list-container">
        {data.map((item, idx) => renderItem(item, idx))}
      </div>
    );
  }

  return (
    <div className={`infinite-list-page ${className}`} style={style} ref={scrollRef}>
      <div className="infinite-list-content">
        {listBody}
        {/* 加载中 */}
        {loading && (
          <div className="infinite-list-loading">
            <Spin />
            <span style={{ marginLeft: 8, color: '#666' }}>加载中</span>
          </div>
        )}
        {/* 没有更多数据 */}
        {!hasMore && data.length > 0 && (
          <div className="infinite-list-no-more">
            {noMoreText || getDefaultNoMoreText()}
          </div>
        )}
      </div>
    </div>
  );
}

export default InfiniteList;
