import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import './index.less';

interface LoadingProps {
  text?: string;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  text = i18next.t('j-dingtalk-web_components_Loading_Loading'),
  className = '',
}) => {
  return (
    <div className={`loading-container ${className}`}>
      <div className="loading-content">
        <div className="loading-dots">
          <div className="dot dot-1" />
          <div className="dot dot-2" />
          <div className="dot dot-3" />
        </div>
        {text && (
          <div className="loading-text">{text}</div>
        )}
      </div>
    </div>);
};

export default Loading;
