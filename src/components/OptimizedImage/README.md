# OptimizedImage 组件

一个优化的图片组件，支持懒加载、渐进式加载、图片优化等功能，并特别针对阿里云CDN的访问控制问题进行了优化。

## 功能特性

- 🚀 懒加载支持
- 🎨 渐进式加载
- 📱 响应式图片优化
- 🛡️ CDN访问控制问题自动处理
- 🔄 错误重试机制
- ⚡ 预加载优化

## 解决的问题

### 阿里云CDN 403 Forbidden 错误

当遇到以下错误时：
```
状态代码: 403 Forbidden
x-tengine-error: denied by Referer ACL
```

本组件提供了以下解决方案：

1. **自动Referrer Policy处理**: 默认使用 `no-referrer` 策略
2. **智能重试机制**: 自动尝试不同的referrer策略
3. **灵活配置**: 支持手动指定referrer策略

## 使用方法

### 基础用法

```tsx
import OptimizedImage from '@/components/OptimizedImage';

// 基础使用 - 自动处理CDN访问控制
<OptimizedImage
  src="https://cbu01.alicdn.com/O1CN01RBv4wy2FyF2MOgrLQ_!!4003588948-0-cib.jpg"
  alt="商品图片"
  width={300}
  height={200}
/>
```

### 处理CDN访问控制问题

```tsx
// 方案1: 使用默认的no-referrer策略（推荐）
<OptimizedImage
  src="https://cbu01.alicdn.com/xxx.jpg"
  alt="图片"
  width={300}
  height={200}
  // referrerPolicy默认为'no-referrer'，无需手动设置
/>

// 方案2: 手动指定referrer策略
<OptimizedImage
  src="https://cbu01.alicdn.com/xxx.jpg"
  alt="图片"
  width={300}
  height={200}
  referrerPolicy="origin"
/>

// 方案3: 启用自动重试（组件会自动尝试不同策略）
<OptimizedImage
  src="https://cbu01.alicdn.com/xxx.jpg"
  alt="图片"
  width={300}
  height={200}
  onError={() => console.log('所有重试策略都失败了')}
/>
```

### 高级功能

```tsx
// 渐进式加载
<OptimizedImage
  src="https://example.com/large-image.jpg"
  alt="大图"
  width={800}
  height={600}
  progressive={true}
  quality={90}
  format="webp"
/>

// 自定义占位符
<OptimizedImage
  src="https://example.com/image.jpg"
  alt="图片"
  placeholder="/placeholder.jpg"
  lazy={true}
  onLoad={() => console.log('图片加载完成')}
  onError={() => console.log('图片加载失败')}
/>
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | string | - | 图片URL |
| alt | string | - | 图片alt属性 |
| width | number/string | - | 图片宽度 |
| height | number/string | - | 图片高度 |
| referrerPolicy | string | 'no-referrer' | Referrer策略，用于处理CDN访问控制 |
| lazy | boolean | true | 是否启用懒加载 |
| progressive | boolean | false | 是否启用渐进式加载 |
| quality | number | 80 | 图片质量(1-100) |
| format | string | 'webp' | 图片格式 |
| placeholder | string | - | 占位符图片URL |
| onLoad | function | - | 图片加载成功回调 |
| onError | function | - | 图片加载失败回调 |
| onClick | function | - | 点击事件回调 |

## Referrer Policy 选项

- `no-referrer`: 不发送referrer（推荐用于CDN问题）
- `origin`: 只发送源域名
- `no-referrer-when-downgrade`: HTTPS到HTTP时不发送referrer
- `origin-when-cross-origin`: 跨域时只发送源域名
- `same-origin`: 同源时发送完整referrer
- `strict-origin`: 严格源策略
- `strict-origin-when-cross-origin`: 跨域时严格源策略
- `unsafe-url`: 总是发送完整referrer

## 错误处理机制

组件内置了智能重试机制：

1. 首次加载失败时，自动尝试 `origin` 策略
2. 仍然失败时，尝试 `no-referrer-when-downgrade` 策略
3. 所有策略都失败后，显示错误状态

## 注意事项

1. **阿里云CDN图片**: 组件会自动检测阿里云CDN链接并应用最佳策略
2. **性能优化**: 使用了预加载和平滑过渡效果
3. **兼容性**: 支持现代浏览器的所有referrer策略
4. **错误恢复**: 提供了优雅的错误处理和重试机制

## 故障排除

### 403 Forbidden 错误
如果仍然遇到403错误：
1. 确认图片URL是否正确
2. 尝试手动设置不同的`referrerPolicy`
3. 考虑使用后端代理服务

### 图片加载缓慢
1. 启用`progressive`渐进式加载
2. 调整`quality`参数
3. 使用合适的`format`格式 
