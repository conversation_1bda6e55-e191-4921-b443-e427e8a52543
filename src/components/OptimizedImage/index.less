.optimized-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: var(--common_level7_base_color);

  .loading-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--common_level7_base_color);
    transition: opacity var(--common_normal_motion_duration) var(--common_normal_motion_timing_function);

    .shimmer-placeholder {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        var(--common_level7_base_color) 25%,
        var(--common_level6_base_color) 50%,
        var(--common_level7_base_color) 75%
      );
      background-size: 300% 100%;
              animation: shimmer 2s ease-in-out infinite;
        opacity: 0.6;
      }

      .low-quality-placeholder,
      .static-placeholder {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .low-quality-placeholder {
        filter: blur(5px);
        transform: scale(1.1);
      }
    }

    &.loaded {
      .loading-placeholder {
        opacity: 0;
        pointer-events: none;
      }
    }

    // Smooth transition for image loading
    img {
      transition: opacity var(--common_normal_motion_duration) var(--common_normal_motion_timing_function);
    }
  }

  .optimized-image-error {
    background-color: var(--common_level7_base_color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--common_level3_base_color);
    font-size: var(--common_footnote_text_style__font-size);
    // border: 1px dashed var(--common_level5_base_color);

  .error-content {
    text-align: center;
    padding: 8px;

    span {
      display: block;
      margin-top: 4px;
    }

    &::before {
      content: '📷';
      font-size: 24px;
      display: block;
      margin-bottom: 4px;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: 300% 0;
  }
  50% {
    background-position: -100% 0;
  }
  100% {
    background-position: -300% 0;
  }
}

// Responsive image styles
.optimized-image-container {
  img {
    max-width: 100%;
    height: auto;
    transition: opacity var(--common_light_motion_duration) var(--common_light_motion_timing_function);
  }

  // Hover effects for interactive images
  &[role="button"],
  &.clickable {
    cursor: pointer;

    &:hover {
      img {
        transform: scale(1.02);
        transition: transform var(--common_light_motion_duration) var(--common_light_motion_timing_function);
      }
    }
  }

  // Subtle loading state indicator (only show after delay)
  &:not(.loaded) {
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 1px solid transparent;
      border-top: 1px solid var(--common_level4_base_color);
      border-radius: 50%;
      animation: spin 1.5s linear infinite;
      z-index: 10;
      opacity: 0;
      animation-delay: 1s;
    }

    &.show-spinner::after {
      opacity: 0.7;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 