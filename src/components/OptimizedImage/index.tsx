import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect } from 'react';
import './index.less';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  fit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  lazy?: boolean;
  progressive?: boolean;
  quality?: number;
  format?: 'webp' | 'jpg' | 'png';
  placeholder?: string;
  referrerPolicy?: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  onLoad?: () => void;
  onError?: () => void;
  onClick?: () => void;
}

// Check if URL is an Alibaba Cloud CDN
const isAlibabaCloudCDN = (url: string): boolean => {
  return url.includes('img.alicdn.com') || url.includes('gw.alicdn.com');
};

// Image URL optimization utility
const optimizeImageUrl = (
  url: string,
  options: {
    width?: number | string;
    height?: number | string;
    quality?: number;
    format?: string;
  } = {},
) => {
  if (!url || typeof url !== 'string') return url;

  const { width, height, quality = 80, format = 'webp' } = options;

  // For Alibaba CDN images - check for exact domain match to avoid false positives
  if (isAlibabaCloudCDN(url)) {
    const params = [];
    if (width) params.push(`w_${width}`);
    if (height) params.push(`h_${height}`);
    params.push(`q_${quality}`);
    if (format) params.push(`f_${format}`);

    // Add image processing parameters
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}x-oss-process=image/resize,m_fill,${params.join(',')}`;
  }

  return url;
};

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  fit = 'cover',
  lazy = true,
  progressive = false,
  quality = 80,
  format = 'webp',
  placeholder,
  referrerPolicy = 'no-referrer',
  onLoad,
  onError,
  onClick,
}) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [inView, setInView] = useState(!lazy);
  const [showSpinner, setShowSpinner] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [currentReferrerPolicy, setCurrentReferrerPolicy] = useState(referrerPolicy);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const spinnerTimerRef = useRef<NodeJS.Timeout>();

  // Referrer policies to try in case of CDN access control issues
  const referrerPolicies: Array<'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url'> = ['no-referrer', 'origin', 'no-referrer-when-downgrade'];

  // Optimize image URL
  const optimizedSrc = optimizeImageUrl(src, {
    width: typeof width === 'number' ? width : undefined,
    height: typeof height === 'number' ? height : undefined,
    quality,
    format,
  });

  // Generate low quality placeholder for progressive loading
  const lowQualitySrc = progressive
    ? optimizeImageUrl(src, {
      width: typeof width === 'number' ? Math.floor(width / 10) : undefined,
      height: typeof height === 'number' ? Math.floor(height / 10) : undefined,
      quality: 20,
      format: 'jpg',
    })
    : undefined;

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || inView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          // Start spinner timer when image starts loading
          spinnerTimerRef.current = setTimeout(() => {
            setShowSpinner(true);
          }, 800);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01,
      },
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
      if (spinnerTimerRef.current) {
        clearTimeout(spinnerTimerRef.current);
      }
    };
  }, [lazy, inView]);

  // Handle retry with different referrer policies
  useEffect(() => {
    if (retryCount === 0 || !inView) return;

    // Reset states for retry
    setLoaded(false);
    setError(false);
  }, [retryCount, inView]);

  const handleLoad = () => {
    setLoaded(true);
    setShowSpinner(false);
    if (spinnerTimerRef.current) {
      clearTimeout(spinnerTimerRef.current);
    }
    // Reset retry count on successful load
    setRetryCount(0);
    onLoad?.();
  };

  const handleError = () => {
    // Try different referrer policies for CDN access control issues
    if (retryCount < referrerPolicies.length - 1 && isAlibabaCloudCDN(src)) {
      const nextPolicy = referrerPolicies[retryCount + 1];
      setCurrentReferrerPolicy(nextPolicy);
      setRetryCount((prev) => prev + 1);
      return;
    }

    // All retry attempts failed
    setError(true);
    setShowSpinner(false);
    if (spinnerTimerRef.current) {
      clearTimeout(spinnerTimerRef.current);
    }
    onError?.();
  };
  const containerStyle: React.CSSProperties = {
    width: width || '100%',
    height: height || '100%',
    position: 'relative',
    overflow: 'hidden',
    ...style,
  };

  const imgStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: fit,
    transition: loaded ? 'opacity 0.3s ease-out' : 'none',
    opacity: loaded ? 1 : 0,
    display: 'block',
  };

  if (error) {
    return (
      <div
        ref={containerRef}
        className={`optimized-image-error ${className}`}
        style={containerStyle}
        onClick={onClick}
      >
        <div className="error-content">
          <span>{i18next.t('j-dingtalk-web_components_OptimizedImage_PortraitFailure')}</span>
        </div>
      </div>
    );
  }

  // Placeholder style with fade out transition
  const placeholderStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    transition: 'opacity 0.3s ease-out',
    opacity: loaded ? 0 : 1,
  };

  // Main image style with fade in transition and higher z-index
  const mainImageStyle: React.CSSProperties = {
    ...imgStyle,
    position: 'absolute',
    top: 0,
    left: 0,
  };

  // Render loading placeholder
  const renderPlaceholder = () => {
    if (progressive && lowQualitySrc) {
      return (
        <img
          src={lowQualitySrc}
          alt={alt}
          className="low-quality-placeholder"
          style={{
            width: '100%',
            height: '100%',
            objectFit: fit,
            filter: 'blur(5px)',
            display: 'block',
          }}
        />
      );
    }

    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt={alt}
          className="static-placeholder"
          style={{
            width: '100%',
            height: '100%',
            objectFit: fit,
            display: 'block',
          }}
        />
      );
    }

    return <div className="shimmer-placeholder" />;
  };

  return (
    <div
      ref={containerRef}
      className={`optimized-image-container ${className} ${loaded ? 'loaded' : ''} ${
        showSpinner ? 'show-spinner' : ''
      }`}
      style={containerStyle}
      onClick={onClick}
    >
      {/* Loading placeholder - always render but fade out when loaded */}
      {!error && (
        <div className="loading-placeholder" style={placeholderStyle}>
          {renderPlaceholder()}
        </div>
      )}

      {/* Main image - positioned above placeholder */}
      {inView && (
        <img
          ref={imgRef}
          key={`${optimizedSrc}-${currentReferrerPolicy}`}
          src={optimizedSrc}
          alt={alt}
          style={mainImageStyle}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy ? 'lazy' : 'eager'}
          decoding="async"
          referrerPolicy={currentReferrerPolicy}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
