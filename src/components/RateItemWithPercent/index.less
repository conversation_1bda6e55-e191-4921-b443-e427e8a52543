.review-item {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;

  .review-name {
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    display: flex;
    justify-self: start;
    color: var(--common_level1_base_color);;
    width: 100px;
  }

  .review-progress-container {
    flex: 1;
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;

    .review-progress-bar {
      height: 100%;
      background-color: #FF0E53;
      border-radius: 3px;
    }
  }

  .review-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 60px;
    justify-content: center;

    .review-count {
      font-size: 12px;
      font-weight: 400;
      line-height: 22px;
      color: var(--common_level1_base_color);
    }

    .review-trend {
      display: flex;
      color:var(--common_level1_base_color);
      align-items: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 22px;

      &.up {
        color: #00c292;
      }

      &.down {
        color: #ff4d6a;
      }

      .trend-arrow {
        font-size: 20px;
      }
    }
  }
}
