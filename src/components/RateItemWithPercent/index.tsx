import React from 'react';
import { RateItemWithPercentProps } from '@/common/types';
import './index.less';

const RateItemWithPercent: React.FC<RateItemWithPercentProps> = ({ item }) => {
  const { feature, percentage } = item;

  return (
    <div className="review-item">
      <div className="review-name">{feature}</div>
      <div className="review-progress-container">
        <div
          className="review-progress-bar"
          style={{ width: `${percentage}%` }}
        />
      </div>
      <div className="review-stats">
        <div className="review-trend">
          {percentage}%
        </div>
      </div>
    </div>
  );
};

export default RateItemWithPercent;
