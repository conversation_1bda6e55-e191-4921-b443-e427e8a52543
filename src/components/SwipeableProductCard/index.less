.swipeable-product-card {
  width: 294px;
  height: 450px;
  position: relative;
  overflow: hidden;

  .product-card {
    width: 100%;
    height: 100%;
    background: var(--common_fg_color);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    touch-action: pan-y;
    will-change: transform, opacity;

    .product-image-container {
      position: relative;
      width: 100%;
      height: 70%;
      overflow: hidden;
      border-radius: 16px 16px 0 0;

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      // Swipe overlays for visual feedback
      .swipe-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
        transition: opacity 0.2s ease;

        .overlay-content {
          text-align: center;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

          .overlay-icon {
            font-size: 48px;
            margin-bottom: 8px;
          }

          .overlay-text {
            font-size: 18px;
            font-weight: 600;
          }
        }

        &.not-interested {
          background: linear-gradient(
            135deg,
            rgba(255, 59, 48, 0.8) 0%,
            rgba(255, 149, 0, 0.8) 100%
          );
        }

        &.interested {
          background: linear-gradient(
            135deg,
            rgba(52, 199, 89, 0.8) 0%,
            rgba(48, 209, 88, 0.8) 100%
          );
        }
      }
    }

    .product-info {
      padding: 16px;
      height: 20%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;

      .product-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--common_level1_base_color);
        line-height: 22px;
        margin-bottom: 8px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-detail-bottom {
        .product-price {
          .price {
            font-size: 20px;
            font-weight: 600;
            color: #ff0e53;
          }
        }
      }

      // Audit mask overlay
      .audit-mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 0 16px 16px;
        z-index: 10;

        .audit-buttons {
          display: flex;
          gap: 12px;

          .audit-btn {
            min-width: 80px;
            height: 36px;
            border-radius: 18px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;

            &.approve-btn {
              background: #52c41a;
              border-color: #52c41a;
              color: white;

              &:hover {
                background: #73d13d;
                border-color: #73d13d;
              }

              &:active {
                background: #389e0d;
                border-color: #389e0d;
              }
            }

            &.reject-btn {
              background: #ff4d4f;
              border-color: #ff4d4f;
              color: white;

              &:hover {
                background: #ff7875;
                border-color: #ff7875;
              }

              &:active {
                background: #d9363e;
                border-color: #d9363e;
              }
            }
          }
        }
      }
    }

    .action-buttons {
      position: absolute;
      bottom: 16px;
      left: 16px;
      right: 16px;
      display: flex;
      gap: 12px;
      z-index: 10;

      .action-btn {
        flex: 1;
        height: 44px;
        border: none;
        border-radius: 22px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);

        &:active {
          transform: scale(0.95);
        }

        &.not-interested-btn {
          background: rgba(255, 255, 255, 0.9);
          color: var(--common_level1_base_color);
          border: 1px solid var(--common_line_hard_color);

          &:hover {
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        &.interested-btn {
          background: var(--common_level1_base_color);
          color: white;

          &:hover {
            background: var(--common_level2_base_color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }

    // Hover effects for desktop
    @media (hover: hover) {
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
      }
    }
  }
}

// Animation classes for card transitions
.swipeable-product-card {
  .product-card {
    &.swipe-up-exit {
      animation: swipeUpExit 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }

    &.swipe-down-exit {
      animation: swipeDownExit 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    }
  }
}

@keyframes swipeUpExit {
  to {
    transform: translateY(-100vh) scale(0.8);
    opacity: 0;
  }
}

@keyframes swipeDownExit {
  to {
    transform: translateY(100vh) scale(0.8);
    opacity: 0;
  }
}

// Responsive design
@media (max-width: 480px) {
  .swipeable-product-card {
    width: 280px;
    height: 420px;

    .product-card {
      .product-info {
        padding: 12px;

        .product-title {
          font-size: 14px;
          line-height: 20px;
        }

        .product-price .price {
          font-size: 18px;
        }
      }

      .action-buttons {
        bottom: 12px;
        left: 12px;
        right: 12px;
        gap: 8px;

        .action-btn {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 360px) {
  .swipeable-product-card {
    width: 260px;
    height: 390px;
  }
}
