import React, { useState, useRef } from 'react';
import { IOriginProduct } from '@/common/types';
import OptimizedImage from '@/components/OptimizedImage';
import { Button } from 'dingtalk-design-mobile';
import { i18next } from '@ali/dingtalk-i18n';
import './index.less';

interface SwipeableProductCardProps {
  product: IOriginProduct;
  onInterested: (product: IOriginProduct) => void;
  onNotInterested: (product: IOriginProduct) => void;
  onProductClick: (product: IOriginProduct) => void;
  onProductAudit?: (product: IOriginProduct, status: number) => void;
  hasAuditPermission?: boolean;
  className?: string;
}

const SwipeableProductCard: React.FC<SwipeableProductCardProps> = ({
  product,
  onInterested,
  onNotInterested,
  onProductClick,
  onProductAudit,
  hasAuditPermission = false,
  className = '',
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const [translateY, setTranslateY] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Swipe threshold for triggering actions
  const SWIPE_THRESHOLD = 100;
  const MAX_TRANSLATE = 150;

  // Handle touch start
  const handleTouchStart = (e: React.TouchEvent) => {
    if (isAnimating) return;

    setIsDragging(true);
    setStartY(e.touches[0].clientY);
    setCurrentY(e.touches[0].clientY);
  };

  // Handle touch move
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || isAnimating) return;

    const newY = e.touches[0].clientY;
    const deltaY = newY - startY;

    // Limit the translation to prevent excessive movement
    const limitedDeltaY = Math.max(-MAX_TRANSLATE, Math.min(MAX_TRANSLATE, deltaY));

    setCurrentY(newY);
    setTranslateY(limitedDeltaY);
  };

  // Handle touch end
  const handleTouchEnd = () => {
    if (!isDragging || isAnimating) return;

    setIsDragging(false);
    const deltaY = currentY - startY;

    if (Math.abs(deltaY) > SWIPE_THRESHOLD) {
      // Trigger action based on swipe direction
      setIsAnimating(true);

      if (deltaY < 0) {
        // Swipe up - Not interested
        setTranslateY(-window.innerHeight);
        setTimeout(() => {
          onNotInterested(product);
          resetCard();
        }, 300);
      } else {
        // Swipe down - Interested
        setTranslateY(window.innerHeight);
        setTimeout(() => {
          onInterested(product);
          resetCard();
        }, 300);
      }
    } else {
      // Reset position if threshold not met
      setTranslateY(0);
    }
  };

  // Reset card position
  const resetCard = () => {
    setTranslateY(0);
    setIsAnimating(false);
    setIsDragging(false);
  };

  // Handle button clicks
  const handleNotInterestedClick = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setTranslateY(-window.innerHeight);
    setTimeout(() => {
      onNotInterested(product);
      resetCard();
    }, 300);
  };

  const handleInterestedClick = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setTranslateY(window.innerHeight);
    setTimeout(() => {
      onInterested(product);
      resetCard();
    }, 300);
  };

  // Handle audit button clicks
  const handleAuditApprove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onProductAudit) {
      onProductAudit(product, 1); // 1: 通过
    }
  };

  const handleAuditReject = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onProductAudit) {
      onProductAudit(product, -1); // -1: 不通过
    }
  };

  // Get card style based on current state
  const getCardStyle = () => {
    const opacity = Math.max(0.3, 1 - Math.abs(translateY) / 200);
    const scale = Math.max(0.9, 1 - Math.abs(translateY) / 500);

    return {
      transform: `translateY(${translateY}px) scale(${scale})`,
      opacity,
      transition: isDragging ? 'none' : 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    };
  };

  // Get overlay style for visual feedback
  const getOverlayStyle = () => {
    if (Math.abs(translateY) < 30) return { opacity: 0 };

    const opacity = Math.min(0.8, Math.abs(translateY) / 100);
    return { opacity };
  };

  return (
    <div className={`swipeable-product-card ${className}`}>
      <div
        ref={cardRef}
        className="product-card"
        style={getCardStyle()}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={() => !isDragging && onProductClick(product)}
      >

        {/* Product Image */}
        <div className="product-image-container">
          <OptimizedImage
            src={product.primaryImage || ''}
            alt={product.title || ''}
            width="100%"
            height="100%"
            lazy
            progressive
            quality={90}
            className="product-image"
          />


          {/* Swipe Overlays */}
          <div
            className="swipe-overlay not-interested"
            style={translateY < -30 ? getOverlayStyle() : { opacity: 0 }}
          >

            <div className="overlay-content">
              <div className="overlay-icon">👎</div>
              <div className="overlay-text">{i18next.t('j-dingtalk-web_components_SwipeableProductCard_NotInterested')}</div>
            </div>
          </div>

          <div
            className="swipe-overlay interested"
            style={translateY > 30 ? getOverlayStyle() : { opacity: 0 }}
          >

            <div className="overlay-content">
              <div className="overlay-icon">👍</div>
              <div className="overlay-text">{i18next.t('j-dingtalk-web_components_SwipeableProductCard_Interested')}</div>
            </div>
          </div>
        </div>

        {/* Product Info */}
        <div className="product-info">
          <div className="product-title">
            {product.titleTranslated || product.title || ''}
          </div>
          <div className="product-detail-bottom">
            <div className="product-price">
              {product.priceInfo?.price &&
              <span className="price">
                ¥{product.priceInfo.price}
              </span>
              }
            </div>
          </div>

          {/* Audit Mask and Buttons - only show for users with audit permission */}
          {hasAuditPermission && (
            <div className="audit-mask">
              <div className="audit-buttons">
                <Button
                  type="primary"
                  size="small"
                  className="audit-btn approve-btn"
                  onClick={handleAuditApprove}
                >
                  {i18next.t('j-dingtalk-web_components_SwipeableProductCard_Approve')}
                </Button>
                <Button
                  type="secondary"
                  size="small"
                  className="audit-btn reject-btn"
                  onClick={handleAuditReject}
                >
                  {i18next.t('j-dingtalk-web_components_SwipeableProductCard_Reject')}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="action-buttons">
          <button
            className="action-btn not-interested-btn"
            onClick={(e) => {
              e.stopPropagation();
              handleNotInterestedClick();
            }}
          >{i18next.t('j-dingtalk-web_components_SwipeableProductCard_NotInterested')}


          </button>
          <button
            className="action-btn interested-btn"
            onClick={(e) => {
              e.stopPropagation();
              handleInterestedClick();
            }}
          >{i18next.t('j-dingtalk-web_components_SwipeableProductCard_Interested')}


          </button>
        </div>
      </div>
    </div>);
};

export default SwipeableProductCard;
