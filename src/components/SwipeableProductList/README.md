# SwipeableProductList 相册式滑动商品列表组件

一个支持相册式滑动体验的商品列表组件，具有渐隐过渡效果和侧边预览功能。

## 功能特性

### 🎯 核心功能
- **相册式滑动** - 类似相册的左右滑动切换体验
- **渐隐过渡效果** - 滑动时的平滑透明度和缩放变化
- **侧边预览** - 左右两边各显示25px宽度的相邻商品
- **上下滑动表示兴趣** - 上滑不感兴趣，下滑感兴趣
- **按钮操作** - 点击按钮也可以表示兴趣
- **动画效果** - 流畅的滑动和消失动画
- **响应式设计** - 适配移动端和桌面端

### 📐 设计规格
- **卡片尺寸**: 294×450px (桌面端)
- **卡片间距**: 16px
- **侧边预览**: 左右各显示25px宽度
- **渐隐效果**: 侧边卡片透明度60%，缩放90%

### 📱 交互方式
1. **左右滑动** - 切换到上一个/下一个商品，带有实时预览效果
2. **上滑** - 标记当前商品为"不感兴趣"并切换到下一个
3. **下滑** - 标记当前商品为"感兴趣"并切换到下一个
4. **点击按钮** - 直接操作兴趣状态（仅当前卡片可操作）
5. **点击商品** - 查看商品详情或切换到对应商品
6. **键盘操作** - 左右方向键切换商品

### ✨ 视觉效果
- **当前卡片**: 100%透明度，100%缩放，居中显示
- **侧边卡片**: 60%透明度，90%缩放，部分显示
- **滑动时**: 实时调整透明度和缩放比例
- **过渡动画**: 0.3s 三次贝塞尔曲线过渡
- **层级关系**: 当前卡片 z-index 最高

### 💡 智能提示系统
- **首次显示**: 仅在用户首次使用时显示操作提示
- **自动隐藏**: 4秒后自动隐藏，或用户开始交互时立即隐藏
- **持久记忆**: 使用 localStorage 记录提示显示状态
- **优雅降级**: localStorage 不可用时静默处理

## 使用方法

### 基本用法

```tsx
import SwipeableProductList from '@/components/SwipeableProductList';
import { IOriginProduct } from '@/common/types';

const MyComponent = () => {
  const products: IOriginProduct[] = [
    // 你的商品数据
  ];

  const handleProductInterested = (product: IOriginProduct) => {
    console.log('用户对商品感兴趣:', product);
    // 处理感兴趣逻辑
  };

  const handleProductNotInterested = (product: IOriginProduct) => {
    console.log('用户对商品不感兴趣:', product);
    // 处理不感兴趣逻辑
  };

  const handleProductClick = (product: IOriginProduct) => {
    console.log('用户点击商品:', product);
    // 打开商品详情页
  };

  return (
    <SwipeableProductList
      products={products}
      onProductInterested={handleProductInterested}
      onProductNotInterested={handleProductNotInterested}
      onProductClick={handleProductClick}
    />
  );
};
```

### Props 接口

```tsx
interface SwipeableProductListProps {
  products: IOriginProduct[];                              // 商品列表数据
  onProductInterested: (product: IOriginProduct) => void;  // 感兴趣回调
  onProductNotInterested: (product: IOriginProduct) => void; // 不感兴趣回调
  onProductClick: (product: IOriginProduct) => void;       // 商品点击回调
  className?: string;                                      // 自定义样式类名
}
```

### 商品数据结构

```tsx
interface IOriginProduct {
  productId?: string;           // 商品ID
  title?: string;              // 商品标题
  titleTranslated?: string;    // 翻译后的标题
  primaryImage?: string;       // 主图片URL
  detailHtml?: string;        // 详情页链接
  priceInfo?: {               // 价格信息
    price: string;
  };
}
```

## 样式定制

### CSS 变量
组件使用 CSS 变量来支持主题定制：

```less
.swipeable-product-list {
  // 使用项目的设计系统变量
  --common_fg_color: #ffffff;
  --common_level1_base_color: #333333;
  --common_level2_base_color: #666666;
  // ... 更多变量
}
```

### 自定义样式
可以通过 `className` prop 添加自定义样式：

```tsx
<SwipeableProductList
  className="my-custom-product-list"
  // ... 其他 props
/>
```

```less
.my-custom-product-list {
  // 自定义样式
  .product-card {
    border-radius: 20px;
  }
}
```

## 技术实现

### 核心技术
- **React Hooks** - 状态管理和生命周期
- **Touch Events** - 触摸手势识别
- **CSS Transforms** - 流畅动画效果
- **Flexbox Layout** - 响应式布局
- **LocalStorage API** - 持久化用户偏好设置

### 性能优化
- **防抖处理** - 避免频繁的状态更新
- **动画优化** - 使用 `transform` 和 `opacity` 实现硬件加速
- **内存管理** - 及时清理事件监听器和定时器

### LocalStorage 管理
```tsx
// 工具函数封装localStorage操作
const getHintShownStatus = (): boolean => {
  try {
    return localStorage.getItem('swipeable_product_list_hint_shown') === 'true';
  } catch (error) {
    return false; // 优雅降级
  }
};

const setHintShownStatus = (): void => {
  try {
    localStorage.setItem('swipeable_product_list_hint_shown', 'true');
  } catch (error) {
    // 静默处理错误（如隐私浏览模式）
  }
};
```

### 兼容性
- **移动端** - iOS Safari, Android Chrome
- **桌面端** - Chrome, Firefox, Safari, Edge
- **触摸设备** - 支持多点触控

## 注意事项

1. **数据要求** - 确保 `products` 数组不为空
2. **图片优化** - 建议使用 `OptimizedImage` 组件加载图片
3. **回调处理** - 在回调函数中处理异步操作时注意错误处理
4. **性能考虑** - 大量商品时考虑虚拟滚动优化

## 示例场景

### 电商推荐
```tsx
// 商品推荐页面
<SwipeableProductList
  products={recommendedProducts}
  onProductInterested={(product) => {
    // 添加到收藏夹
    addToFavorites(product.productId);
    // 发送埋点
    sendAnalytics('product_interested', product);
  }}
  onProductNotInterested={(product) => {
    // 添加到不感兴趣列表
    addToDisliked(product.productId);
    // 发送埋点
    sendAnalytics('product_not_interested', product);
  }}
  onProductClick={(product) => {
    // 跳转到商品详情页
    navigateToProduct(product.productId);
  }}
/>
```

### 内容发现
```tsx
// 内容发现页面
<SwipeableProductList
  products={discoveryProducts}
  onProductInterested={handleLike}
  onProductNotInterested={handleDislike}
  onProductClick={handleViewDetails}
  className="discovery-product-list"
/>
```
