.swipeable-product-list {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  background: var(--common_fg_press_color);

  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--common_fg_color);
    border-radius: 12px 12px 0 0;
    margin-bottom: 8px;

    .pagination {
      font-size: 14px;
      font-weight: 500;
      color: var(--common_level1_base_color);
      padding: 6px 12px;
      background: var(--common_bg_color);
      border-radius: 16px;
    }

    .navigation-dots {
      display: flex;
      gap: 8px;
      align-items: center;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: none;
        background: var(--common_level4_base_color);
        cursor: pointer;
        transition: all 0.2s ease;

        &.active {
          background: var(--common_level1_base_color);
          transform: scale(1.2);
        }

        &:hover:not(.active) {
          background: var(--common_level3_base_color);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }

  .product-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    touch-action: pan-y;
    display: flex;
    align-items: center;
    justify-content: center;

    .cards-wrapper {
      position: relative;
      width: 294px; // Single card width
      height: 450px; // Single card height
      display: flex;
      align-items: center;
      justify-content: center;

      .card-wrapper {
        position: absolute;
        width: 294px;
        height: 450px;
        top: 0;
        left: 0;
        will-change: transform, opacity;

        &.prev {
          cursor: pointer;

          // Disable interactions for side cards
          .swipeable-product-card {
            pointer-events: none;

            .action-buttons {
              display: none;
            }
          }
        }

        &.current {
          // Current card is fully interactive
        }

        &.next {
          cursor: pointer;

          // Disable interactions for side cards
          .swipeable-product-card {
            pointer-events: none;

            .action-buttons {
              display: none;
            }
          }
        }
      }
    }
  }

  .navigation-arrows {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px; // Wider than card to position arrows outside
    height: 44px;
    pointer-events: none;
    z-index: 10;

    .nav-arrow {
      position: absolute;
      top: 0;
      width: 44px;
      height: 44px;
      border: none;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.9);
      color: var(--common_level1_base_color);
      font-size: 24px;
      font-weight: bold;
      cursor: pointer;
      pointer-events: auto;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &:active:not(:disabled) {
        transform: scale(0.95);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        pointer-events: none;
      }

      &.prev {
        left: 0;
      }

      &.next {
        right: 0;
      }
    }
  }

  .swipe-hint {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 20;
    animation: fadeInUp 0.5s ease;
    opacity: 1;
    transition: opacity 0.3s ease;

    .hint-text {
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 16px;
      border-radius: 20px;
      font-size: 12px;
      text-align: center;
      line-height: 18px;
      backdrop-filter: blur(10px);
      white-space: nowrap;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }

  // Empty state
  &.empty {
    .empty-state {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .empty-icon {
        font-size: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .empty-text {
        font-size: 16px;
        color: var(--common_level2_base_color);
        text-align: center;
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .swipeable-product-list {
    .navigation-arrows {
      display: none; // Hide arrows on mobile, rely on touch gestures
    }

    .list-header {
      padding: 8px 12px;
      margin-bottom: 4px;

      .pagination {
        font-size: 12px;
        padding: 4px 8px;
      }

      .navigation-dots .dot {
        width: 6px;
        height: 6px;
      }
    }
  }
}

@media (max-width: 480px) {
  .swipeable-product-list {
    .list-header {
      .navigation-dots {
        gap: 6px;
      }
    }

    .swipe-hint .hint-text {
      font-size: 11px;
      padding: 8px 12px;
    }

    .product-container {
      .cards-wrapper {
        width: 280px; // Slightly smaller on mobile
        height: 420px;

        .card-wrapper {
          width: 280px;
          height: 420px;
        }
      }
    }
  }
}

// Ensure cards maintain aspect ratio on very small screens
@media (max-width: 360px) {
  .swipeable-product-list {
    .product-container {
      .cards-wrapper {
        width: 260px;
        height: 390px;

        .card-wrapper {
          width: 260px;
          height: 390px;
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .swipeable-product-list {
    .navigation-arrows .nav-arrow {
      background: rgba(44, 44, 46, 0.9);
      color: white;

      &:hover:not(:disabled) {
        background: rgba(44, 44, 46, 1);
      }
    }
  }
}
