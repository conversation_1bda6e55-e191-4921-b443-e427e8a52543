import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect } from 'react';
import { IOriginProduct } from '@/common/types';
import SwipeableProductCard from '@/components/SwipeableProductCard';
import './index.less';

// LocalStorage key for hint display control
const SWIPE_HINT_SHOWN_KEY = 'swipeable_product_list_hint_shown';

// Utility functions for localStorage operations
const getHintShownStatus = (): boolean => {
  try {
    return localStorage.getItem(SWIPE_HINT_SHOWN_KEY) === 'true';
  } catch (error) {
    return false; // Default to not shown if localStorage is unavailable
  }
};

const setHintShownStatus = (): void => {
  try {
    localStorage.setItem(SWIPE_HINT_SHOWN_KEY, 'true');
  } catch (error) {

    // Silently fail if localStorage is unavailable
  }
};

interface SwipeableProductListProps {
  products: IOriginProduct[];
  onProductInterested: (product: IOriginProduct) => void;
  onProductNotInterested: (product: IOriginProduct) => void;
  onProductClick: (product: IOriginProduct) => void;
  onProductAudit?: (product: IOriginProduct, status: number) => void;
  hasAuditPermission?: boolean;
  className?: string;
}

const SwipeableProductList: React.FC<SwipeableProductListProps> = ({
  products,
  onProductInterested,
  onProductNotInterested,
  onProductClick,
  onProductAudit,
  hasAuditPermission = false,
  className = '',
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [dragOffset, setDragOffset] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const currentXRef = useRef(0);
  const isDraggingRef = useRef(false);

  // Handle horizontal swipe for product navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    if (isTransitioning) return;

    startXRef.current = e.touches[0].clientX;
    currentXRef.current = e.touches[0].clientX;
    isDraggingRef.current = true;

    // Hide hint when user starts interacting
    if (showHint) {
      setShowHint(false);
      setHintShownStatus();
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDraggingRef.current || isTransitioning) return;

    currentXRef.current = e.touches[0].clientX;
    const deltaX = currentXRef.current - startXRef.current;

    // Update drag offset for real-time visual feedback
    setDragOffset(deltaX);
  };

  const handleTouchEnd = () => {
    if (!isDraggingRef.current || isTransitioning) return;

    isDraggingRef.current = false;
    const deltaX = currentXRef.current - startXRef.current;
    const threshold = 80; // Minimum swipe distance

    // Reset drag offset
    setDragOffset(0);

    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0 && currentIndex > 0) {
        // Swipe right - previous product
        navigateToProduct(currentIndex - 1);
      } else if (deltaX < 0 && currentIndex < products.length - 1) {
        // Swipe left - next product
        navigateToProduct(currentIndex + 1);
      }
    }
  };

  // Navigate to specific product
  const navigateToProduct = (index: number) => {
    if (index < 0 || index >= products.length || isTransitioning) return;

    setIsTransitioning(true);
    setCurrentIndex(index);

    // Hide hint when user navigates
    if (showHint) {
      setShowHint(false);
      setHintShownStatus();
    }

    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  // Handle product actions
  const handleProductInterested = (product: IOriginProduct) => {
    onProductInterested(product);
    // Move to next product after action
    if (currentIndex < products.length - 1) {
      setTimeout(() => navigateToProduct(currentIndex + 1), 100);
    }
  };

  const handleProductNotInterested = (product: IOriginProduct) => {
    onProductNotInterested(product);
    // Move to next product after action
    if (currentIndex < products.length - 1) {
      setTimeout(() => navigateToProduct(currentIndex + 1), 100);
    }
  };

  // Initialize hint display state
  useEffect(() => {
    const hintShown = getHintShownStatus();
    if (!hintShown && currentIndex === 0) {
      setShowHint(true);
      // Auto hide hint after 4 seconds
      const timer = setTimeout(() => {
        setShowHint(false);
        setHintShownStatus();
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [currentIndex]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isTransitioning) return;

      switch (e.key) {
        case 'ArrowLeft':
          if (currentIndex > 0) {
            navigateToProduct(currentIndex - 1);
          }
          break;
        case 'ArrowRight':
          if (currentIndex < products.length - 1) {
            navigateToProduct(currentIndex + 1);
          }
          break;
        default:
          // No action for other keys
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentIndex, products.length, isTransitioning]);

  if (!products.length) {
    return (
      <div className="swipeable-product-list empty">
        <div className="empty-state">
          <div className="empty-icon">📦</div>
          <div className="empty-text">{i18next.t('j-dingtalk-web_components_SwipeableProductList_NoProductDataAvailable')}</div>
        </div>
      </div>);
  }

  // Calculate visible products (current, previous, next)
  const getVisibleProducts = () => {
    const visibleProducts = [];

    // Previous product
    if (currentIndex > 0) {
      visibleProducts.push({
        product: products[currentIndex - 1],
        position: 'prev',
        index: currentIndex - 1,
      });
    }

    // Current product
    visibleProducts.push({
      product: products[currentIndex],
      position: 'current',
      index: currentIndex,
    });

    // Next product
    if (currentIndex < products.length - 1) {
      visibleProducts.push({
        product: products[currentIndex + 1],
        position: 'next',
        index: currentIndex + 1,
      });
    }

    return visibleProducts;
  };

  const visibleProducts = getVisibleProducts();

  return (
    <div className={`swipeable-product-list ${className}`}>
      {/* Header with pagination */}
      <div className="list-header">
        <div className="pagination">
          {currentIndex + 1}/{products.length}
        </div>
        <div className="navigation-dots">
          {products.map((product, index) =>
            (<button
              key={product.productId || `dot-${index}`}
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => navigateToProduct(index)}
              disabled={isTransitioning}
            />))}
        </div>
      </div>

      {/* Product cards container */}
      <div
        ref={containerRef}
        className="product-container"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >

        <div className="cards-wrapper" style={{ transform: `translateX(${dragOffset * 0.3}px)` }}>
          {visibleProducts.map(({ product, position, index }) => {
            // Calculate transform and opacity based on position and drag
            const getCardStyle = () => {
              const cardWidth = 294;
              const cardGap = 16;
              const sidePreview = 25;

              let translateX = 0;
              let opacity = 1;
              let scale = 1;
              let zIndex = 1;

              if (position === 'prev') {
                translateX = -(cardWidth + cardGap - sidePreview);
                opacity = 0.6;
                scale = 0.9;
                zIndex = 1;
              } else if (position === 'current') {
                translateX = 0;
                opacity = 1;
                scale = 1;
                zIndex = 3;
              } else if (position === 'next') {
                translateX = cardWidth + cardGap - sidePreview;
                opacity = 0.6;
                scale = 0.9;
                zIndex = 1;
              }

              // Apply drag effect
              if (isDraggingRef.current) {
                const dragProgress = dragOffset / (cardWidth + cardGap);

                if (position === 'prev') {
                  opacity = Math.min(1, 0.6 + Math.max(0, dragProgress) * 0.4);
                  scale = Math.min(1, 0.9 + Math.max(0, dragProgress) * 0.1);
                } else if (position === 'next') {
                  opacity = Math.min(1, 0.6 + Math.max(0, -dragProgress) * 0.4);
                  scale = Math.min(1, 0.9 + Math.max(0, -dragProgress) * 0.1);
                }
              }

              return {
                transform: `translateX(${translateX}px) scale(${scale})`,
                opacity,
                zIndex,
                transition: isDraggingRef.current ? 'none' : 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
              };
            };

            return (
              <div
                key={product.productId}
                className={`card-wrapper ${position}`}
                style={getCardStyle()}
              >

                <SwipeableProductCard
                  product={product}
                  onInterested={position === 'current' ? handleProductInterested : () => {}}
                  onNotInterested={position === 'current' ? handleProductNotInterested : () => {}}
                  onProductClick={position === 'current' ? onProductClick : () => navigateToProduct(index)}
                  onProductAudit={position === 'current' ? onProductAudit : undefined}
                  hasAuditPermission={hasAuditPermission}
                />

              </div>);
          })}
        </div>
      </div>

      {/* Navigation arrows for desktop */}
      <div className="navigation-arrows">
        <button
          className="nav-arrow prev"
          onClick={() => navigateToProduct(currentIndex - 1)}
          disabled={currentIndex === 0 || isTransitioning}
        >

          ‹
        </button>
        <button
          className="nav-arrow next"
          onClick={() => navigateToProduct(currentIndex + 1)}
          disabled={currentIndex === products.length - 1 || isTransitioning}
        >

          ›
        </button>
      </div>

      {/* Swipe hint - only show once */}
      {showHint &&
      <div className="swipe-hint">
        <div className="hint-text">{i18next.t('j-dingtalk-web_components_SwipeableProductList_SlideLeftAndRightTo')}
          <br />{i18next.t('j-dingtalk-web_components_SwipeableProductList_IMNotInterestedIn')}

        </div>
      </div>
      }
    </div>);
};

export default SwipeableProductList;
