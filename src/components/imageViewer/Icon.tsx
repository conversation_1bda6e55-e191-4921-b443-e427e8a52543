import * as React from 'react';

interface Props {
  type: string;
  size?: number;
}

function Icon(props: Props) {
  const { type, size } = props;
  const iconSize = size || 16;
  let result = null;

  switch (type) {
    case 'zoom-in':
      result = (
        <svg
          className="svg-icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10479"
          width={iconSize}
          height={iconSize}
        >
          <path
            d="M455.168 64a391.168 391.168 0 0 1 298.752 643.84l196.992 196.928c11.072 11.136 11.904 28.8 3.2 41.28l-3.648 4.48a32.64 32.64 0 0 1-45.696 0.384L707.84 753.92A391.168 391.168 0 1 1 455.232 64z m0 65.216a326.016 326.016 0 1 0 0 651.968 326.016 326.016 0 0 0 0-651.968z m0 163.008c18.048 0 32.64 13.952 32.64 32.448l-0.064 97.92h97.984c16 0 29.184 11.968 32 26.88l0.448 5.76c0 17.92-14.016 32.576-32.448 32.576L487.744 487.68v97.984a32.896 32.896 0 0 1-26.88 32l-5.696 0.448a32.192 32.192 0 0 1-32.576-32.448V487.744h-97.92a32.896 32.896 0 0 1-32.448-32.576c0-17.92 13.952-32.576 32.448-32.576h97.92v-97.92c0-16 11.968-29.184 26.88-32l5.76-0.448z"
            p-id="10480"
          />
        </svg>
      );
      break;
    case 'zoom-out':
      result = (
        <svg
          className="svg-icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10606"
          width={iconSize}
          height={iconSize}
        >
          <path
            d="M455.168 64a391.168 391.168 0 0 1 298.752 643.84l196.992 196.928c11.072 11.136 11.904 28.8 3.2 41.28l-3.648 4.416a32.64 32.64 0 0 1-45.696 0.448L707.84 753.92A391.168 391.168 0 1 1 455.232 64z m0 65.216a326.016 326.016 0 1 0 0 651.968 326.016 326.016 0 0 0 0-651.968z m130.56 293.376c16 0 29.184 11.968 32 26.88l0.448 5.76c0 17.92-14.016 32.576-32.448 32.576h-261.12a32.896 32.896 0 0 1-32.384-32.64c0-17.92 13.952-32.576 32.448-32.576h261.12z"
            p-id="10607"
          />
        </svg>
      );
      break;
    case 'rotate-left':
      result = (
        <svg
          className="svg-icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10733"
          width={iconSize}
          height={iconSize}
        >
          <path
            d="M727.041463 368.420571a145.554286 145.554286 0 0 1 145.554286 145.700572v364.251428c0 80.457143-65.170286 145.627429-145.554286 145.627429H145.482606A145.554286 145.554286 0 0 1 0.001463 878.299429v-364.251429c0-80.457143 65.097143-145.554286 145.408-145.554286h581.705143z m0 72.850286H145.482606a72.777143 72.777143 0 0 0-72.704 72.850286v364.251428c0 40.228571 32.548571 72.777143 72.704 72.777143h581.705143a72.777143 72.777143 0 0 0 72.704-72.850285v-364.251429a72.777143 72.777143 0 0 0-72.704-72.777143zM470.310034 10.752a36.425143 36.425143 0 0 1 0 51.492571l-22.528 22.454858c259.657143-23.405714 506.148571 141.897143 575.780572 401.92l1.097143 6.436571a36.425143 36.425143 0 0 1-69.193143 18.578286l-2.267429-6.217143C889.418606 267.483429 654.630034 120.978286 416.18432 162.084571l54.418286 54.564572a36.352 36.352 0 0 1-51.419429 51.346286l-103.131428-103.131429a36.352 36.352 0 0 1 0-51.346286L418.817463 10.752a36.425143 36.425143 0 0 1 51.492571 0z"
            p-id="10734"
          />
        </svg>
      );
      break;
    case 'rotate-right':
      result = (
        <svg
          className="svg-icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10860"
          width={iconSize}
          height={iconSize}
        >
          <path
            d="M297.770423 368.420571A145.554286 145.554286 0 0 0 152.216137 514.194286v364.251428c0 80.457143 65.170286 145.627429 145.554286 145.627429h581.632A145.554286 145.554286 0 0 0 1024.810423 878.299429v-364.251429c0-80.457143-65.097143-145.554286-145.408-145.554286H297.69728z m0 72.850286h581.632c40.155429 0 72.704 32.621714 72.704 72.850286v364.251428c0 40.228571-32.548571 72.777143-72.704 72.777143H297.69728a72.777143 72.777143 0 0 1-72.704-72.850285v-364.251429c0-40.228571 32.548571-72.777143 72.704-72.777143zM554.501851 10.752a36.425143 36.425143 0 0 0 0 51.492571l22.528 22.454858C317.372709 61.293714 70.88128 226.596571 1.24928 486.619429l-1.097143 6.436571a36.425143 36.425143 0 0 0 69.193143 18.578286l2.267429-6.217143C135.39328 267.483429 370.181851 120.978286 608.627566 162.084571l-54.418286 54.564572a36.352 36.352 0 0 0 51.419429 51.346286l103.131428-103.131429a36.352 36.352 0 0 0 0-51.346286L605.994423 10.752a36.425143 36.425143 0 0 0-51.492572 0z"
            p-id="10861"
          />
        </svg>
      );
      break;
    case 'download':
      result = (
        <svg
          className="svg-icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="10987"
          width={iconSize}
          height={iconSize}
        >
          <path
            d="M926.500571 585.142857c25.673143 0 46.811429 19.894857 48.64 45.129143l0.073143 3.657143v194.998857a146.285714 146.285714 0 0 1-140.8 146.212571l-5.485714 0.073143H195.072a146.285714 146.285714 0 0 1-146.212571-140.8l-0.073143-5.485714V633.929143a48.786286 48.786286 0 0 1 97.353143-3.657143l0.146285 3.657143v194.998857c0 25.746286 19.894857 46.811429 45.129143 48.64l3.657143 0.146286h633.856c25.746286 0 46.811429-19.894857 48.64-45.129143l0.146286-3.657143V633.929143c0-26.989714 21.796571-48.786286 48.786285-48.786286zM512 24.356571c26.916571 0 48.786286 21.869714 48.786286 48.786286v479.232l158.061714-158.061714a48.786286 48.786286 0 0 1 68.973714 68.900571l-241.371428 241.371429a48.786286 48.786286 0 0 1-68.900572 0l-241.371428-241.371429a48.786286 48.786286 0 0 1 68.973714-68.900571l157.988571 158.061714L463.286857 73.142857c0-26.916571 21.869714-48.786286 48.786286-48.786286z"
            p-id="10988"
          />
        </svg>
      );
      break;
    default:
      break;
  }

  return result;
}

export default Icon;
