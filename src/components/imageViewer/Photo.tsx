
import Icon from './Icon';

interface Props {
  width: string;
  height: string;
  disableView?: boolean;
  onClick: () => void;
  imageUrl?: string;
}

function Photo(props: Props) {
  const { width, height, disableView, onClick, imageUrl } = props;
  const photoStyle = {
    width,
    height,
  };
  return (
    <div
      className="yida-m-image-viewer--photo"
      onClick={() => {
        if (disableView) return;
        onClick?.();
      }}
      style={photoStyle}
    >
      {disableView ? null : (
        <div className="photo-before">
          <Icon type="zoom-in" size={16} />
        </div>
      )}
      <div className="photo-item">
        <img src={imageUrl} />
      </div>
    </div>
  );
}

export default Photo;
