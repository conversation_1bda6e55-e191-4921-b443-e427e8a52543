
import ImageViewer from '../imageViewer';

export interface ImagePreviewPhotoItem {
  previewSrc?: string;
  downloadUrl?: string;
  src: string;
}

export interface ImagePreviewOptions {
  current?: number;
  photos?: { data: ImagePreviewPhotoItem[] } | ImagePreviewPhotoItem[];
  onError?: (err: any) => void;
}

export function isAbsoluteUrl(url: string): boolean {
  return (`${url}` || '').indexOf('//') > -1;
}
export function getAbsoluteUrl(url: string): string {
  if (isAbsoluteUrl(url) || !url) {
    return url;
  }

  const { origin, protocol, host, hostname, port } = window.location;
  const finalPort = port === '80' || port === '443' ? '' : port;
  const prefix = origin || `${protocol}//${host || [hostname, finalPort].filter((v) => v).join(':')}`;

  if (url[0] === '/') {
    return `${prefix}${url}`;
  }
  return `${prefix}/${url}`;
}
function removeParamFromUrl(url: string, paramKey: string, condition: (val: string) => boolean = () => true) {
  try {
    const urlObj = new URL(getAbsoluteUrl(url));
    if (urlObj.searchParams) {
      const paramValue = urlObj.searchParams.get(paramKey);
      if (paramValue && (!condition || condition(paramValue))) {
        urlObj.searchParams.delete(paramKey);
      }
    }
    return urlObj.toString();
  } catch (e) {
    return url;
  }
}

export function previewImages(options: ImagePreviewOptions) {
  const newOptions = {
    ...options,
    photos: {
      // @ts-ignore
      data: (options.photos?.data || options.photos || []).map((item) => {
        // 去除可能带有的 process=image/resize 参数
        const src = removeParamFromUrl(item.previewSrc || item.src, 'process', (val) =>
          (val || '').startsWith('image/resize'));

        return {
          ...item,
          previewSrc: src,
          src,
        };
      }),
    },
  };

  ImageViewer.show(newOptions);
}
