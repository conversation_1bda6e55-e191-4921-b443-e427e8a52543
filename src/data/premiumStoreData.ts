export interface StoreInfo {
  name: string;
  ranking: number;
  overallRating: number;
  productQuality: string;
  shippingSpeed: string;
  customerService: string;
  avatar: string;
}

export interface Product {
  id: string;
  title: string;
  price: string;
  originalPrice?: string;
  image: string;
  salesCount: string;
  isLiked?: boolean;
}

// Mock data for premium store
export const mockStoreInfo: StoreInfo = {
  name: '深圳市千顾伊科技有限公司',
  ranking: 1,
  overallRating: 4.0,
  productQuality: '4.0',
  shippingSpeed: '4.0',
  customerService: '3.0',
  avatar:
    'https://img.alicdn.com/imgextra/i1/O1CN01yM4Y8e29Vwu4ExXd0_!!6000000008074-2-tps-64-64.png',
};

export const mockProducts: Product[] = [
  {
    id: '1',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '1120',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN01T73jNR1cuKfUFmpr1_!!6000000003660-2-tps-656-984.png',
    salesCount: '月销量215件',
    isLiked: true,
  },
  {
    id: '2',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '1990',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN01fC3ah0244rOm6cXGj_!!6000000007338-2-tps-656-848.png',
    salesCount: '月销量215件',
  },
  {
    id: '3',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '8880',
    image: 'https://img.alicdn.com/imgextra/i3/O1CN01L3J19P20KCCIBLuuq_!!6000000006830-2-tps-708-956.png',
    salesCount: '月销量215件',
    isLiked: true,
  },
  {
    id: '4',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '4990',
    image: 'https://img.alicdn.com/imgextra/i4/O1CN01VNDym11DmfeHpQOmK_!!6000000000259-2-tps-656-848.png',
    salesCount: '月销量215件',
  },
];
