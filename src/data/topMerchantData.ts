// Mock data for top merchant page
export interface MerchantInfo {
  id: string;
  name: string;
  avatar: string;
  fansCount: string;
  transactionCount: string;
  likesCount: string;
  notesCount: number;
  brandCount: number;
  followCount: number;
}

export interface StyleContent {
  id: string;
  image: string;
  description: string;
  likes: number;
}

// Add new interface for style notes list
export interface StyleNote {
  id: string;
  image: string;
  description: string;
  likes: number;
  relatedProduct: Product;
}

export interface Product {
  id: string;
  title: string;
  price: string;
  dspcCode: string;
  originalPrice?: string;
  image: string;
  salesCount: string;
  isLiked?: boolean;
}

export const mockMerchantInfo: MerchantInfo = {
  id: '1',
  name: '大冤种的收纳大法大冤种的收纳大法大冤种的收纳大法大冤种的收纳大法大冤种的收纳大法大冤种的收纳大法',
  avatar: 'https://img.alicdn.com/imgextra/i1/O1CN01PudCfK1KQs6kk5ubk_!!6000000001159-2-tps-208-208.png',
  fansCount: '188万',
  transactionCount: '800万',
  likesCount: '200.77万',
  notesCount: 97,
  brandCount: 5,
  followCount: 20,
};

export const mockStyleContent: StyleContent = {
  id: '1',
  image: 'https://img.alicdn.com/imgextra/i4/O1CN01DIy9tv1PXoBZpPAH1_!!6000000001851-2-tps-448-336.png',
  description: '超实用收纳思路，这才是极致。超实用收纳思路，这才是极致，超实用收纳思路，这才是极致。超实用收纳思路，这才是极致，超实用收纳思路，这才是极致。超实用收纳思路，这才是极致，超实用收纳思路，这才是极致。超实用收纳思路，这才是极致',
  likes: 515,
};

// Add mock data for style notes list based on design
export const mockStyleNotes: StyleNote[] = [
  {
    id: '1',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN01T73jNR1cuKfUFmpr1_!!6000000003660-2-tps-656-984.png',
    description: '超实用收纳思路，这才是极致。超实用收纳思路，超实用收纳思路，这才是极致。超实用收纳思路',
    likes: 515,
    relatedProduct: {
      id: '1',
      title: '类似商品',
      price: '¥231',
      image: 'https://img.alicdn.com/imgextra/i1/O1CN01fC3ah0244rOm6cXGj_!!6000000007338-2-tps-656-848.png',
      salesCount: '',
    },
  },
  {
    id: '2',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN01T73jNR1cuKfUFmpr1_!!6000000003660-2-tps-656-984.png',
    description: '超实用收纳思路，这才是极致。超实用收纳思路，超实用收纳思路，这才是极致。超实用收纳思路',
    likes: 515,
    relatedProduct: {
      id: '1',
      title: '类似商品',
      price: '¥231',
      image: 'https://img.alicdn.com/imgextra/i1/O1CN01fC3ah0244rOm6cXGj_!!6000000007338-2-tps-656-848.png',
      salesCount: '',
    },
  },
  {
    id: '3',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN01T73jNR1cuKfUFmpr1_!!6000000003660-2-tps-656-984.png',
    description: '超实用收纳思路，这才是极致。超实用收纳思路，超实用收纳思路，这才是极致。超实用收纳思路',
    likes: 515,
    relatedProduct: {
      id: '1',
      title: '类似商品',
      price: '¥231',
      image: 'https://img.alicdn.com/imgextra/i1/O1CN01fC3ah0244rOm6cXGj_!!6000000007338-2-tps-656-848.png',
      salesCount: '',
    },
  },
];

export const mockRecommendProducts: Product[] = [
  {
    id: '1',
    title: '',
    price: '¥231',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN012XNEiV1EwZpZIY2TC_!!6000000000416-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '2',
    title: '',
    price: '¥199',
    image: 'https://img.alicdn.com/imgextra/i3/O1CN01OOW6er1VdewMF9OPJ_!!6000000002676-2-tps-424-424.png',
    salesCount: '',
  },
  {
    id: '3',
    title: '',
    price: '¥200',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN01S4Nu6m1Jcww7cWRfL_!!6000000001050-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '4',
    title: '',
    price: '¥299',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN018a6rkh1G56pDDpQm8_!!6000000000570-2-tps-384-384.png',
    salesCount: '',
  },
  {
    id: '5',
    title: '',
    price: '¥231',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN012XNEiV1EwZpZIY2TC_!!6000000000416-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '6',
    title: '',
    price: '¥199',
    image: 'https://img.alicdn.com/imgextra/i3/O1CN01OOW6er1VdewMF9OPJ_!!6000000002676-2-tps-424-424.png',
    salesCount: '',
  },
  {
    id: '7',
    title: '',
    price: '¥200',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN01S4Nu6m1Jcww7cWRfL_!!6000000001050-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '8',
    title: '',
    price: '¥299',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN018a6rkh1G56pDDpQm8_!!6000000000570-2-tps-384-384.png',
    salesCount: '',
  },
  {
    id: '9',
    title: '',
    price: '¥231',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN012XNEiV1EwZpZIY2TC_!!6000000000416-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '10',
    title: '',
    price: '¥199',
    image: 'https://img.alicdn.com/imgextra/i3/O1CN01OOW6er1VdewMF9OPJ_!!6000000002676-2-tps-424-424.png',
    salesCount: '',
  },
  {
    id: '11',
    title: '',
    price: '¥200',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN01S4Nu6m1Jcww7cWRfL_!!6000000001050-2-tps-352-352.png',
    salesCount: '',
  },
  {
    id: '12',
    title: '',
    price: '¥299',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN018a6rkh1G56pDDpQm8_!!6000000000570-2-tps-384-384.png',
    salesCount: '',
  },
];

export const mockMerchantProducts: Product[] = [
  {
    id: '1',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '1120',
    image: 'https://img.alicdn.com/imgextra/i2/O1CN01T73jNR1cuKfUFmpr1_!!6000000003660-2-tps-656-984.png',
    salesCount: '月销量215件',
    isLiked: true,
  },
  {
    id: '2',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '1990',
    image: 'https://img.alicdn.com/imgextra/i1/O1CN01fC3ah0244rOm6cXGj_!!6000000007338-2-tps-656-848.png',
    salesCount: '月销量215件',
  },
  {
    id: '3',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '8880',
    image: 'https://img.alicdn.com/imgextra/i3/O1CN01L3J19P20KCCIBLuuq_!!6000000006830-2-tps-708-956.png',
    salesCount: '月销量215件',
    isLiked: true,
  },
  {
    id: '4',
    title: 'NEW新型ダウンシリーズNEW新型ダウンシリーズNEW新型ダウンシリーズ',
    price: '4990',
    image: 'https://img.alicdn.com/imgextra/i4/O1CN01VNDym11DmfeHpQOmK_!!6000000000259-2-tps-656-848.png',
    salesCount: '月销量215件',
  },
];
