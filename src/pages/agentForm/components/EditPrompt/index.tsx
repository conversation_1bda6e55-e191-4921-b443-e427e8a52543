import React, { useState } from 'react';
import MarkdownEditor from '../MarkdownEditor';
// import './index.less';

interface FormValues {
  promptName: string;
  prompt: string;
  modelName: string;
  goodCasefiles: string[];
  badCasefiles: string[];
}

const EditPrompt: React.FC = () => {
  const [prompt, setPrompt] = useState('');

  return (
    <>
      <MarkdownEditor value={prompt} onChange={setPrompt} />
    </>
  );
};

export default EditPrompt;
