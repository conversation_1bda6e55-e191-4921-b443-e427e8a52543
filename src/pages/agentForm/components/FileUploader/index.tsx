import { i18next } from '@ali/dingtalk-i18n'; import React, { useState } from 'react';
import { Upload, Button, message, Modal } from 'antd';
import axios from 'axios';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { v4 as uuidv4 } from 'uuid';
import OSS from 'ali-oss';
import './index.less';

interface FileUploaderProps {
  value?: string[];
  onChange?: (fileList: string[]) => void;
  maxCount?: number;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  value = [],
  onChange,
  maxCount = 1,
}) => {
  console.log(value, 'value');
  const [fileList, setFileList] = useState<UploadFile[]>([...value]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [loading, setLoading] = useState(false);
  // 模拟OSS客户端配置
  // 实际使用时需要从后端获取STS临时凭证
  const getOssClient = () => {
    // 这里仅作示例，实际应用中应从后端获取临时凭证
    return new OSS({
      region: 'oss-cn-hangzhou',
      accessKeyId: 'your-access-key-id',
      accessKeySecret: 'your-access-key-secret',
      bucket: 'your-bucket-name',
      secure: true,
    });
  };
  // useEffect(() => {setFileList(value)}, [value]);
  const customRequest = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    try {
      // 实际应用中，这里应该先向自己的服务器请求上传凭证
      // const { credentials } = await fetch('/api/oss/token').then(res => res.json());
      // const client = new OSS(credentials);

      // 为了示例，这里直接模拟上传成功
      // 实际应用中，应该使用以下代码上传到OSS
      // const key = `uploads/${uuidv4()}-${file.name}`;
      // const result = await client.multipartUpload(key, file, {
      //   progress: (p) => {
      //     onProgress({ percent: p * 100 });
      //   }
      // });
      // file.name = `${uuidv4()}-${file.name}`;
      const formData = new FormData();
      formData.append('file', file);
      // await fetch('https://pre-fluxion.dingtalk.com/v1/oss/file/getOssMeta',{
      //    method: 'GET',
      //    headers: {
      //     'API-KEY': 'Y85AWgoG45swao4FswVZWc99OWOn7fW9',
      //     'Content-Type': 'application/json',
      //     // 可以添加更多的头部信息
      //   },
      // })
      setLoading(true);
      const url = window.location.href.indexOf('pre') > -1 ? 'https://pre-fluxion.dingtalk.com/v1/oss/file/upload' : 'https://fluxion.dingtalk.com/v1/oss/file/upload';
      const result = await axios.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'API-KEY': 'Y85AWgoG45swao4FswVZWc99OWOn7fW9',
        },
      }).then((res) => {
        return res.data;
      });
      // const result = await fetch('https://pre-fluxion.dingtalk.com/v1/oss/file/upload', {
      //   method: 'POST', // 或者 'POST', 'PUT', 'DELETE' 等
      //   headers: {
      //     'API-KEY': 'Y85AWgoG45swao4FswVZWc99OWOn7fW9',
      //     // 'Content-Type': 'multipart/form-data'
      //     // 可以添加更多的头部信息
      //   },
      //   body: formData,
      // });
      console.log(result);
      if (!result.success) {
        setLoading(false);
        message.error(i18next.t('j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed', { fileName: file.name }));
        return;
      }
      console.log(result);
      // 更新文件列表
      const newFile = {
        uid: uuidv4(),
        name: result.fileUrl,
        status: 'done',
        // url:'https://pre-fluxion.dingtalk.com/v1/oss/file/download?fileName='+name,
      } as UploadFile;
      setLoading(false);
      const newFileList = [...fileList, newFile];
      setFileList(newFileList);
      // 更新表单值
      if (onChange) {
        console.log('onChange', newFileList);
        const urls = newFileList.map((file) => file.name || '');
        // console.log(urls);
        onChange(urls);
      }

      onSuccess(newFile, new XMLHttpRequest());
      message.success(i18next.t('j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully', { fileName: file.name }));
      // console.log(res);
    } catch (error) {
      console.error(i18next.t('j-agent-web_pages_agentForm_components_FileUploader_UploadFailed'), error);
      onError(error);
      message.error(i18next.t('j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed', { fileName: file.name }));
    }
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      return;
    }

    const isImage = file.type?.startsWith('image');

    if (isImage) {
      setPreviewImage(file.url || file.preview);
      setPreviewVisible(true);
      setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
    } else {
      // 非图片文件，直接在新窗口打开
      if (file.url) {
        window.open(file.url);
      }
    }
  };

  const handleCancel = () => setPreviewVisible(false);

  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter((item) => item.uid !== file.uid);
    setFileList(newFileList);

    if (onChange) {
      const urls = newFileList.map((file) => file.url || '').filter(Boolean);
      onChange(urls);
    }

    return true;
  };
  console.log(fileList, 'fileList');
  const uploadProps: UploadProps = {
    fileList,
    customRequest,
    onPreview: handlePreview,
    onRemove: handleRemove,
    multiple: false,
    maxCount: 1,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
    accept: '.pth,.png',
  };

  return (
    <div className="file-uploader">
      <Upload {...uploadProps} listType="text">
        <Button icon={<UploadOutlined />} disabled={fileList.length >= maxCount || loading} loading={loading}>{i18next.t('j-agent-web_pages_agentForm_components_FileUploader_UploadFiles')}

        </Button>
        <div className="upload-hint">{i18next.t('j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle')}
          {1}{i18next.t('j-agent-web_pages_agentForm_components_FileUploader_Files')}
        </div>
      </Upload>

      <Modal
        visible={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
      >

        <img alt={i18next.t('j-agent-web_pages_agentForm_components_FileUploader_PreviewImage')} style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>);
};

export default FileUploader;
