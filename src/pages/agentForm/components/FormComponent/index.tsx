import { i18next } from '@ali/dingtalk-i18n'; import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Space, Divider, Select, Modal } from 'antd';
import { SaveOutlined, ClearOutlined } from '@ant-design/icons';
import MarkdownEditor from '../MarkdownEditor';
import { getCustomModelList, saveCustomPromptModel, getCustomModel } from '../../services';
import FileUploader from '../FileUploader';
import './index.less';

interface FormValues {
  prompt: string;
  modelName: string;
  modelfile: string[];
}

const FormComponent: React.FC = (props) => {
  const { tabKey } = props;
  const [currentModel, setCurrentModel] = useState('');
  const [form] = Form.useForm<FormValues>();
  const [loading, setLoading] = useState(false);
  const [modelList, setModelList] = useState([]);


  useEffect(() => {
    form.resetFields();
    setCurrentModel('');
    getCustomModelList().then((res) => {
      setModelList(res?.modelUuidList?.map((item: any) => ({
        value: item,
        label: item,
      })));
    });
  },
  [tabKey]);
  const initialValues: FormValues = {
    prompt: '',
    modelName: '',
    modelfile: [],
  };

  const handleSubmit = async (values: FormValues) => {
    setLoading(true);
    try {
      saveCustomPromptModel({ ...values, modelfile: values.modelfile?.[0] || '', modelUuid: currentModel || null }).then((res) => {
        if (!currentModel) {
          Modal.success({
            content: (
              <>
                <div>{i18next.t('j-agent-web_pages_agentForm_components_FormComponent_ModelId')}</div>
                <div>{res.modelUuid}</div>
              </>
            ),
            okText: i18next.t('j-agent-web_pages_agentForm_components_FormComponent_Copy'),
            onOk: async () => {
              // 基础使用
              await navigator.clipboard.writeText(res.modelUuid);
              message.success(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully'));
            },
          });
          form.resetFields();
        } else {
          message.success(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully'));
        }
      }).catch((err) => {
        // form.resetFields();
        message.error(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr', { err }));
      });
    } catch (error) {
      console.error(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError'), error);
      message.error(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry'));
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info(i18next.t('j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset'));
  };
  const onChange = (value: string) => {
    setCurrentModel(value);
    getCustomModel({ modelUuid: value }).then((res) => {
      form.setFieldsValue({
        modelName: res.modelName,
        prompt: res.prompt,
        modelfile: [{
          name: res.modelfile,
        }],
      });
    });
  };
  return (
    <>
      {tabKey === '2' &&
      <>
        <Divider orientation="left">{i18next.t('j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate')}</Divider>
        <Select
          showSearch
          style={{ width: '100%', marginBottom: '16px' }}
          placeholder={i18next.t('j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated')}
          optionFilterProp="label"
          onChange={onChange}
          options={modelList}
        />

      </>
      }
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSubmit}
        className="custom-form"
      >

        <div className="form-section">
          <Form.Item
            name="modelName"
            label={i18next.t('j-agent-web_pages_agentForm_components_FormComponent_ModelName')}
            rules={[{ required: true, message: i18next.t('j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName') }]}
          >

            <Input placeholder={i18next.t('j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName')} maxLength={100} disabled={tabKey === '2'} />
          </Form.Item>
        </div>

        {
          tabKey === '1' && <div className="form-section">
            <Divider orientation="left">{i18next.t('j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles')}</Divider>
            {/* <div style={{ fontSize: '12px', color: 'red' }}>请上传good case图片zip包</div> */}
            <Form.Item
              name="modelfile"
              valuePropName="modelfile"
            >

              <FileUploader />
            </Form.Item>
          </div>
        }

        <div className="form-section">
          <Divider orientation="left">{i18next.t('j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown')}</Divider>
          <Form.Item
            name="prompt"
            rules={[{ required: true, message: i18next.t('j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt') }]}
          >

            <MarkdownEditor />
          </Form.Item>
        </div>

        <div className="form-footer">
          <Space size="middle">
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >

              {currentModel ? i18next.t('j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate') : i18next.t('j-agent-web_pages_agentForm_components_FormComponent_ModelUpload')}
            </Button>
            <Button
              onClick={handleReset}
              icon={<ClearOutlined />}
            >{i18next.t('j-agent-web_pages_agentForm_components_FormComponent_Reset')}
            </Button>
          </Space>
        </div>
      </Form>
    </>);
};

export default FormComponent;
