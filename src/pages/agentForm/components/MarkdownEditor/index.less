.markdown-editor {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;

  .markdown-toolbar {
    display: flex;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;

    .ant-btn {
      margin-right: 4px;
    }
  }

  .ant-tabs-nav {
    margin-bottom: 0;
  }

  .ant-tabs-content {
    padding: 0;
  }

  .ant-input {
    border: none;
    border-radius: 0;
    resize: none;
    padding: 12px;
    min-height: 200px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  }

  .markdown-preview {
    padding: 12px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;

    h1, h2, h3, h4, h5, h6 {
      margin-top: 16px;
      margin-bottom: 8px;
    }

    p {
      margin-bottom: 16px;
    }

    ul, ol {
      padding-left: 24px;
      margin-bottom: 16px;
    }

    img {
      max-width: 100%;
    }

    pre {
      background-color: #f6f8fa;
      padding: 12px;
      border-radius: 4px;
      overflow: auto;
    }

    code {
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    }

    blockquote {
      padding-left: 16px;
      border-left: 4px solid #dfe2e5;
      color: #6a737d;
      margin-bottom: 16px;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;

      th, td {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
      }

      th {
        background-color: #f6f8fa;
      }
    }
  }

  .empty-preview {
    color: #bfbfbf;
    text-align: center;
    padding: 40px 0;
  }
}
