import { i18next } from '@ali/dingtalk-i18n'; import React, { useState } from 'react';
import { Input, Tabs, Button, Tooltip } from 'antd';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { BoldOutlined, ItalicOutlined, OrderedListOutlined, UnorderedListOutlined, LinkOutlined } from '@ant-design/icons';
import './index.less';

const { TextArea } = Input;
const { TabPane } = Tabs;

interface MarkdownEditorProps {
  value?: string;
  onChange?: (value: string) => void;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({ value = '', onChange }) => {
  const [activeTab, setActiveTab] = useState('edit');

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (onChange) {
      onChange(newValue);
    }
  };

  const insertMarkdown = (prefix: string, suffix = '') => {
    const textArea = document.querySelector('.markdown-editor textarea') as HTMLTextAreaElement;
    if (!textArea) return;

    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    const selectedText = value.substring(start, end);
    const beforeText = value.substring(0, start);
    const afterText = value.substring(end);

    const newValue = beforeText + prefix + selectedText + suffix + afterText;
    if (onChange) {
      onChange(newValue);
    }

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textArea.focus();
      textArea.setSelectionRange(
        start + prefix.length,
        end + prefix.length,
      );
    }, 0);
  };

  const toolbarItems = [
    {
      icon: <BoldOutlined />,
      title: i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_Bold'),
      action: () => insertMarkdown('**', '**'),
    },
    {
      icon: <ItalicOutlined />,
      title: i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_Italic'),
      action: () => insertMarkdown('*', '*'),
    },
    {
      icon: <OrderedListOutlined />,
      title: i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList'),
      action: () => insertMarkdown('\n1. '),
    },
    {
      icon: <UnorderedListOutlined />,
      title: i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList'),
      action: () => insertMarkdown('\n- '),
    },
    {
      icon: <LinkOutlined />,
      title: i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_Link'),
      action: () => insertMarkdown('[', '](https://)'),
    }];


  return (
    <div className="markdown-editor" style={{ padding: 4 }}>
      <div className="markdown-toolbar">
        {toolbarItems.map((item, index) =>
          (<Tooltip key={index} title={item.title}>
            <Button
              type="text"
              icon={item.icon}
              onClick={item.action}
            />

           </Tooltip>))}
      </div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_Edit')} key="edit" style={{ marginLeft: 8 }}>
          <TextArea
            rows={10}
            value={value}
            onChange={handleChange}
            placeholder={i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported')}
          />

        </TabPane>
        <TabPane tab={i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_Preview')} key="preview">
          <div className="markdown-preview">
            {value ?
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >

                {value}
              </ReactMarkdown> :

              <div className="empty-preview">{i18next.t('j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview')}</div>
            }
          </div>
        </TabPane>
      </Tabs>
    </div>);
};

export default MarkdownEditor;
