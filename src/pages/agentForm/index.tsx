import { i18next } from '@ali/dingtalk-i18n'; import React from 'react';
import { Layout, Typography, Tabs } from 'antd';
import FormComponent from './components/FormComponent';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

const App: React.FC = () => {
  const [tabKey, setTabKey] = React.useState('1');
  const items = [
    {
      key: '1',
      label: i18next.t('j-agent-web_pages_agentForm_AddModel'),
    },
    {
      key: '2',
      label: i18next.t('j-agent-web_pages_agentForm_UpdateModelPrompt'),
    }];

  return (
    <Layout>
      <Content className="app-container">
        <Tabs activeKey={tabKey} onChange={setTabKey} items={items} />
        <div className="form-container">
          <FormComponent tabKey={tabKey} />
        </div>
      </Content>
    </Layout>);
};

export default App;
