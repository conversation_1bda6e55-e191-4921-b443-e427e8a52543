import request from '@/apis/base';

export const getCustomModelList = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getCustomModelList', [data]);
};
export const saveCustomPromptModel = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/saveCustomPromptModel', [data]);
};
export const getCustomModel = (data: {} = {}) => {
  return request('/r/Adaptor/ProductRpcI/getCustomModel', [data]);
};
