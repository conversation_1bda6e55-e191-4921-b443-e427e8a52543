import { i18next } from '@ali/dingtalk-i18n'; import OSS from 'ali-oss';

/**
 * 获取OSS上传凭证
 * 实际应用中应该从后端获取STS临时凭证
 */
export const getOssConfig = async () => {
  try {
    // 实际项目中应该从后端API获取临时凭证
    // const response = await fetch('/api/oss/token');
    // const data = await response.json();
    // return data.credentials;

    // 这里仅作示例，实际应用请替换为从后端获取的临时凭证
    return {
      region: 'oss-cn-hangzhou',
      accessKeyId: 'temp-access-key-id',
      accessKeySecret: 'temp-access-key-secret',
      stsToken: 'temp-sts-token',
      bucket: 'your-bucket-name',
      secure: true,
      expiration: new Date().getTime() + 3600 * 1000, // 1小时后过期
    };
  } catch (error) {
    console.error(i18next.t('j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration'), error);
    throw new Error(i18next.t('j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials'));
  }
};

/**
 * 上传文件到OSS
 * @param file 要上传的文件
 * @param path 文件在OSS中的路径前缀
 * @param onProgress 上传进度回调
 */
export const uploadToOss = async (
  file: File,
  path = 'uploads',
  onProgress?: (percent: number) => void,
) => {
  try {
    const ossConfig = await getOssConfig();
    const client = new OSS(ossConfig);

    // 生成文件路径
    const date = new Date();
    const dateString = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`;
    const randomString = Math.random().toString(36).substring(2, 10);
    const fileName = `${path}/${dateString}/${randomString}-${file.name}`;

    // 上传文件
    const result = await client.multipartUpload(fileName, file, {
      progress: (p) => {
        if (onProgress) {
          onProgress(Math.floor(p * 100));
        }
      },
    });

    // 返回文件URL
    return {
      url: result.res.requestUrls[0].split('?')[0], // 去掉URL中的查询参数
      name: file.name,
      size: file.size,
    };
  } catch (error) {
    console.error(i18next.t('j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile'), error);
    throw error;
  }
};
