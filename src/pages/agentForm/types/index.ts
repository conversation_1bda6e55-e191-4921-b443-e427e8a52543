// 表单数据类型
export interface FormData {
  title: string;
  author: string;
  email: string;
  phone: string;
  organization: string;
  content: string;
  files: string[];
}

// 文件上传响应类型
export interface UploadResult {
  url: string;
  name: string;
  size: number;
}

// OSS配置类型
export interface OssConfig {
  region: string;
  accessKeyId: string;
  accessKeySecret: string;
  stsToken?: string;
  bucket: string;
  secure: boolean;
  expiration?: number;
}
