import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import $uploadImage from '@ali/dingtalk-jsapi/api/biz/util/uploadImage';
import { AddToSFilled, ResumePreviewOutlined, DeleteOutlined } from '@ali/ding-icons';
import { previewImages } from '@/components/imagesPreview';
import { isDingTalk, configDingTalkImageUpload } from '@/utils/jsapi';
import './index.less';

interface ImageUploaderProps {
  value?: string | null;
  onChange?: (imageUrl: string | null) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ value, onChange }) => {
  const [loading, setLoading] = useState(false);
  const [apiConfigured, setApiConfigured] = useState(false);
  const uploadAreaRef = useRef<HTMLDivElement>(null);

  // Configure DingTalk JS-API on component mount
  useEffect(() => {
    if (isDingTalk()) {
      configDingTalkImageUpload()
        .then(() => {
          setApiConfigured(true);
        })
        .catch((error) => {
        // eslint-disable-next-line no-console
          console.error('Failed to configure DingTalk JS-API:', error);
          setApiConfigured(false);
        });
    } else {
      // Not in DingTalk environment, disable API features
      setApiConfigured(false);
    }
  }, []);

  // Auto focus on upload area when component mounts and no image is uploaded
  useEffect(() => {
    if (!value && uploadAreaRef.current) {
      // Set focus on the upload area for accessibility
      uploadAreaRef.current.focus();
    }
  }, [value]);

  const handlePreview = () => {
    if (value) {
      previewImages({
        photos: [{ src: value }],
        current: 0,
      });
    }
  };

  const handleRemove = () => {
    onChange?.(null);
  };

  const handleUploadClick = () => {
    // Check if in DingTalk environment and API is configured
    if (!isDingTalk()) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_UseThisFeatureInThe'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (!apiConfigured) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_InitializingPleaseTryAgainLater'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    setLoading(true);

    // Use DingTalk biz.util.uploadImage API with .then callback
    $uploadImage({
      multiple: false, // Single image upload
      compression: true, // Disable image compression
      max: 1, // Maximum 1 image
    }).then((result: any) => {
      // Handle upload success
      if (result && result.length > 0) {
        // Get the first uploaded image URL
        const imageUrl = result[0];
        onChange?.(imageUrl);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
      } else {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
      setLoading(false);
    }).catch((error: any) => {
      // Handle upload failure
      console.error(error);
      setLoading(false);
    });
  };

  return (
    <div className="image-uploader">
      {value ?
        <div className="uploaded-image">
          <img src={value} alt="Uploaded" className="preview-img" />
          <div className="image-actions">
            <Button
              size="small"
              onClick={handlePreview}
              className="action-btn preview-btn"
            >

              <ResumePreviewOutlined />
            </Button>
            <Button
              size="small"
              onClick={handleRemove}
              className="action-btn remove-btn danger"
            >

              <DeleteOutlined />
            </Button>
          </div>
        </div> :

        <div
          ref={uploadAreaRef}
          className="upload-area"
          onClick={handleUploadClick}
          tabIndex={0}
          role="button"
          aria-label={i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage')}
          onKeyDown={(e) => {
            // Handle Enter and Space key for accessibility
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleUploadClick();
            }
          }}
        >
          <AddToSFilled className="upload-icon" />
          <div className="upload-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage')}

          </div>
        </div>
      }

      {loading &&
      <div className="upload-loading">
        <div className="loading-spinner" />
        <span>{i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')}</span>
      </div>
      }
    </div>);
};

export default ImageUploader;
