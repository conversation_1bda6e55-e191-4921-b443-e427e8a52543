.video-item {
  width: 100%;
  background-color: #000000;
  overflow: hidden;
  margin-bottom: 16px;

  .video-item-header {
    padding: 16px 0 10px 0;

    .prompt-info-container {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      cursor: pointer;

      .prompt-info {
        flex: 1;
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        line-height: 22px;
        word-break: break-word;
        transition: all 0.3s ease;

        // Collapsed state: single line with ellipsis
        &.collapsed {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // Expanded state: full text display
        &.expanded {
          white-space: normal;
          overflow: visible;
        }
      }

      .prompt-toggle-icon {
        margin-left: 8px;
        color: white;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 22px;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        transform-origin: center center;

        &:hover {
          color: rgba(255,255,255,0.8);
        }

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .video-content {
    position: relative;
    width: 100%;
    border-radius: 12px;
    aspect-ratio: 16/9;
    overflow: hidden;

    .video-container {
      position: relative;
      width: 100%;
      height: 100%;

      .video-player {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 0;
      }

      .video-controls {
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: opacity 0.3s ease;

        .control-left {
          display: flex;
          align-items: center;
          gap: 8px;

          .play-button {
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
            transition: all 0.2s ease;

            &:hover {
              color: #ffffff;
              transform: scale(1.05);
            }
          }

          .time-display {
            color: white;
            font-size: 14px;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            min-width: 80px;
            text-align: left;
            white-space: nowrap;
          }
        }

        .control-right {
          .fullscreen-button {
            border: none;
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
            transition: all 0.2s ease;

            &:hover {
              color: #ffffff;
              transform: scale(1.05);
            }
          }
        }
      }
    }

    .video-placeholder {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 12px;

        .status-text {
          margin: 0;
        }

        .status-processing {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 80%;
          gap: 12px;
        }

        .linear-progress-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          min-width: 280px;

          .progress-title {
            color: white;
            font-size: 14px;
            font-weight: 600;
            line-height: 18px;
            text-align: center;
          }

          .linear-progress {
            width: 100%;

            .progress-track {
              width: 100%;
              height: 4px;
              background-color: rgba(255, 255, 255, 0.5);
              border-radius: 2px;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                background-color: white;
                border-radius: 2px;
                transition: width 0.3s ease;
              }
            }
          }

          .progress-tip {
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
            text-align: center;
            line-height: 18px;
          }
        }
      }
    }
  }

  // Action buttons section
  .action-section.pc-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 24px;
    }

    .completion-time {
      text-align: right;
      color: rgba(255, 255, 255, 0.4);
      font-size: 16px;
      line-height: 22px;
      flex-shrink: 0;
      margin-left: 16px;
    }
  }

  .action-buttons.mobile-layout {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 24px;
    padding: 16px 0;
  }

  .completion-time.mobile-layout {
    text-align: left;
    color: rgba(255,255,255,0.4);
    font-size: 16px;
    line-height: 22px;
  }

  .action-button {
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.9);
    font-size: 24px;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &.loading {
      opacity: 0.6;
      cursor: not-allowed;
      animation: pulse 1.5s ease-in-out infinite;
    }

    &.gif-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      padding: 0 10px;
      color: rgba(255, 255, 255, 0.9);
      border-radius: 190px;
      border: 1px solid rgba(255, 255, 255, 0.24);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    // Like button active state
    &.liked {
      color: #ffffff;
    }

    // Dislike button active state
    &.disliked {
      color: #ffffff;
    }
  }
}

// Animation keyframes
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
