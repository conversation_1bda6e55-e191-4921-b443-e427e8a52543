import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useRef, useCallback } from 'react';
import { Button } from 'dingtalk-design-mobile';
import { RefreshOutlined, EditOutlined } from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import VideoItem from '../VideoItem';
import './index.less';

interface VideoInfo {
  uuid: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  userRating?: string;
  videoFinishTime?: string;
  requestId?: string;
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoListProps {
  videoList: VideoInfo[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate?: (videoInfo: VideoInfo) => void;
  onRefresh?: () => void;
  className?: string;
  progressMap?: Record<string, number>; // Map of video UUID to progress percentage
  hasNextPage?: boolean; // Whether there are more pages to load
  isLoadingMore?: boolean; // Loading state for pagination
  onLoadMore?: () => void; // Function to load more videos
  loadVideoList: () => void; // Load video list
  onOptimizedVideoUpdate?: (
    regenerateResult: any,
    originalUuid: string
  ) => Promise<void>; // Optimized video update
}

const VideoList: React.FC<VideoListProps> = ({
  videoList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  className = '',
  progressMap = {},
  hasNextPage = false,
  isLoadingMore = false,
  onLoadMore,
  loadVideoList,
  onOptimizedVideoUpdate,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle scroll event for infinite loading with throttling
  const handleScrollThrottled = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      if (!hasNextPage || isLoadingMore || !onLoadMore) {
        return;
      }

      let scrollTop: number;
      let scrollHeight: number;
      let clientHeight: number;

      if (isMobileDevice()) {
        // Mobile: use window scroll
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        // PC: use right panel scroll (parent container)
        const rightPanel = containerRef.current?.closest('.ai-video-right-panel') as HTMLElement;
        if (!rightPanel) return;

        scrollTop = rightPanel.scrollTop;
        scrollHeight = rightPanel.scrollHeight;
        clientHeight = rightPanel.clientHeight;
      }

      // Trigger load more when scrolled to 80% of the content
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage >= 0.8) {
        onLoadMore();
      }
    }, 100); // 100ms throttle
  }, [hasNextPage, isLoadingMore, onLoadMore]);

  // Add scroll listener
  useEffect(() => {
    if (isMobileDevice()) {
      // Mobile: listen to window scroll
      window.addEventListener('scroll', handleScrollThrottled);
      return () => {
        window.removeEventListener('scroll', handleScrollThrottled);
      };
    } else {
      // PC: listen to right panel scroll
      const rightPanel = containerRef.current?.closest('.ai-video-right-panel') as HTMLElement;
      if (!rightPanel) return;

      rightPanel.addEventListener('scroll', handleScrollThrottled);
      return () => {
        rightPanel.removeEventListener('scroll', handleScrollThrottled);
      };
    }
  }, [handleScrollThrottled]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Render empty state
  const renderEmptyState = () =>
    (
      <div className="video-list-empty">
        <div className="empty-icon">🎬</div>
        <div className="empty-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable')}</div>
        <div className="empty-description">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi')}

        </div>
        <Button
          type="primary"
          size="large"
          className="create-first-video-btn"
          onClick={onCreateNew}
        >
          {i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo')}
        </Button>
      </div>
    );


  // Render error state
  const renderErrorState = () =>
    (
      <div className="video-list-error">
        <div className="error-icon">❌</div>
        <div className="error-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad')}</div>
        <div className="error-description">
          {error || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo')}
        </div>
        <div className="error-actions">
          {onRefresh &&
          <Button
            type="secondary"
            size="middle"
            onClick={onRefresh}
            className="retry-btn"
          >

            <RefreshOutlined />{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_Retry')}

          </Button>
          }
          <Button
            type="primary"
            size="middle"
            onClick={onCreateNew}
            className="create-new-btn"
          >{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo')}


          </Button>
        </div>
      </div>
    );


  // Render loading state
  const renderLoadingState = () =>
    (
      <div className="video-list-loading">
        <div className="loading-spinner" />
        <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList')}</div>
      </div>
    );


  // Render video list content
  const renderVideoList = () =>
    (
      <div className="video-list-content">
        {/* Header with create button */}
        {
          isMobileDevice() &&
          <div className="video-list-header">
            <div className="header-title">
              <img className="logo" src="https://img.alicdn.com/imgextra/i4/O1CN01zAgMIH1VJ37xaD35D_!!6000000002631-2-tps-173-194.png" alt="arrow" />
              <span className="video-title">AI Video Generation</span>
            </div>
            <EditOutlined className="create-video-btn" onClick={onCreateNew} />
          </div>

        }

        {/* Video list */}
        <div className="video-list-items" ref={containerRef}>
          {videoList.map((videoInfo) =>
            (<VideoItem
              key={videoInfo.uuid}
              videoInfo={videoInfo}
              onRegenerate={onRegenerate}
              loadVideoList={loadVideoList}
              onOptimizedVideoUpdate={onOptimizedVideoUpdate}
              showPromptHeader
              className="video-list-item"
              progress={progressMap[videoInfo.uuid] || 0}
            />))}
        </div>

        {/* Load more section */}
        {videoList.length > 0 &&
        <div className="load-more-section">
          {isLoadingMore &&
          <div className="load-more-loading">
            <div className="loading-spinner" />
            <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore')}</div>
          </div>
          }
          {!isLoadingMore && !hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed')}

          </div>
          }
          {!isLoadingMore && hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore')}

          </div>
          }
        </div>
        }
      </div>
    );


  return (
    <div className={`video-list ${className}`}>
      {isLoading && renderLoadingState()}
      {!isLoading && error && renderErrorState()}
      {!isLoading && !error && videoList.length === 0 && renderEmptyState()}
      {!isLoading && !error && videoList.length > 0 && renderVideoList()}
    </div>);
};

export default VideoList;
