import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import './index.less';

interface WelcomeScreenProps {
  hasGenerateBtn: boolean;
  onGetStarted: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ hasGenerateBtn, onGetStarted }) => {
  return (
    <div className="welcome-screen">
      <div className="welcome-content">
        <div className="logo-section">
          <img
            src="https://img.alicdn.com/imgextra/i4/O1CN01zAgMIH1VJ37xaD35D_!!6000000002631-2-tps-173-194.png"
            alt="logo"
            className="logo-image"
          />
          <h1 className="title">AI Video Generation</h1>
          <p className="subtitle">
            Generating high quality<br />
            e-commerce product videos<br />
            has never been easier
          </p>
        </div>

        {hasGenerateBtn && (
          <Button
            type="primary"
            size="large"
            className="get-started-btn"
            onClick={onGetStarted}
          >
            Get start
          </Button>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
