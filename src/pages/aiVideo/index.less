.ai-video-page {
  background-color: #000000;
  min-height: 100vh;
  overflow: hidden; // Fixed typo: hidde -> hidden
}

// PC Layout Styles
.ai-video-pc-layout {
  display: flex;
  height: 100vh; // Fixed height for the entire layout
  margin: 0 auto;
  background-color: #000000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

  .ai-video-left-panel {
    flex: 0 0 412px; // Fixed width for form panel
    height: 100vh; // Fixed height
    border-right: none;
    background-color: #1d1d1d;
    overflow-y: auto; // Enable vertical scrolling for left panel content
    overflow-x: hidden; // Hide horizontal scrollbar

    // Remove min-height constraint to allow proper scrolling
    > div {
      height: auto; // Let content determine its natural height
    }
  }

  .ai-video-right-panel {
    flex: 1; // Take remaining space
    height: 100vh; // Fixed height
    background-color: var(--common_bg_color); // Match WelcomeScreen background to avoid white line at bottom
    overflow-y: auto; // Enable vertical scrolling for right panel content
    overflow-x: hidden; // Hide horizontal scrollbar
    position: relative;

    // Remove min-height constraint to allow proper scrolling
    > div {
      height: auto; // Let content determine its natural height
    }

    .video-list {
      max-width: 560px;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// Responsive design - on smaller screens, fall back to mobile layout
@media (max-width: 768px) {
  .ai-video-page {
    overflow: auto; // Allow normal scrolling on mobile
  }

  .ai-video-pc-layout {
    flex-direction: column;
    height: auto; // Remove fixed height on mobile
    max-width: none;
    box-shadow: none;

    .ai-video-left-panel {
      flex: none;
      height: auto; // Remove fixed height on mobile
      border: none;
      overflow-y: visible; // Remove scroll on mobile

      > div {
        height: auto; // Natural height on mobile
      }
    }

    .ai-video-right-panel {
      height: auto; // Remove fixed height on mobile
      overflow-y: visible; // Remove scroll on mobile

      > div {
        height: auto; // Natural height on mobile
      }
    }
  }
}
