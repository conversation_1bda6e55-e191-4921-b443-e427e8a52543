import { i18next } from '@ali/dingtalk-i18n';
import { useEffect, useMemo } from 'react';
import { sendUT } from '@/utils/trace';
import { getUrlParam } from '@/utils/util';
import { setPageTitle } from '@/utils/jsapi';
import { generateCardTipsList } from '@/utils/productRankings';
import './index.less';

const App = () => {
  // Get URL parameters
  const platform = getUrlParam('platform');
  const reportType = getUrlParam('reportType');

  // Generate card tips list based on URL parameters
  const cardTipsList = useMemo(() => {
    return generateCardTipsList(platform, reportType);
  }, [platform, reportType]);

  useEffect(() => {
    setPageTitle(i18next.t('j-agent-web_pages_cardTips_DataDescription'));

    sendUT('tips_page_view', {});
  }, []);

  return (
    <div className="card-tips-container">
      <div className="card-tips-list">
        {cardTipsList.map((item) =>
          (
            <div className="card-tips-item" key={item.title}>
              <div className="card-tips-item-title">{item.title}</div>
              <div className="card-tips-item-content">
                {item.content}
              </div>
            </div>
          ))}
      </div>
    </div>);
};

export default App;
