// 混合定义
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-tap-highlight() {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.smooth-scroll() {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.option-base() {
  .flex-center();
  .no-tap-highlight();
  width: calc(25% - 10px);
  margin: 5px;
  text-align: center;
  border: 1px solid var(--button_secondary_line_color);
  border-radius: 6px;
  min-height: 36px;
  font-size: 12px;
  color: var(--common_level1_base_color);
  transition: all 0.2s ease;
  padding: 5px 10px;
  cursor: pointer;
  touch-action: manipulation;
  background-color: var(--button_secondary_bg_color);

  &:hover {
    border-color: #FF0E53;
    color: #FF0E53;
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }

  &.selected,
  &.active {
    color: #FF0E53;
    border-color: #FF0E53;
    background-color: rgba(255, 14, 83, 0.1);
  }
}

.title-base() {
  font-size: 16px;
  font-weight: 600;
  color: var(--common_level1_base_color);
  margin-bottom: 18px;
  margin-left: 12px;
}

// 主容器样式
.category-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  .no-tap-highlight();
  position: relative;

  &.no-data {
    .flex-center();
    height: 100%;
    font-size: 14px;
    color: var(--common_level1_base_color);
  }



  // 内容区域
  &-content {
    .smooth-scroll();
    padding-bottom: 90px;
    padding-bottom: calc(90px + env(safe-area-inset-bottom));
  }
}

// 类目层级样式
.category-level0,
.category-level1,
.category-level2 {
  font-size: 16px;
  font-weight: 600;
  padding: 0 12px;
  margin-top: 18px;
  color: var(--common_level1_base_color);
}

.category-level1,
.category-level2 {
  margin-top: 11px;
}

// 左侧类目列表
.category-list {
  width: 90px;
  background-color: #f8f8f8;
  .smooth-scroll();
}

.category-item {
  padding: 16px 12px;
  font-size: 14px;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
  .no-tap-highlight();

  &.active {
    color: #333;
    background-color: #fff;
    font-weight: 500;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background-color: #ff4d4f;
    }
  }
}

// 右侧选项容器
.options-container {
  flex: 1;
  .smooth-scroll();

  .options-list {
    display: flex;
    flex-wrap: wrap;
    padding: 7px;
  }

  .option-item {
    .option-base();
  }
}

// 底部按钮区域
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 15px;
  padding-bottom: calc(15px + env(safe-area-inset-bottom));
  border-top: 1px solid var(--common_overlay_normal_color);
  background-color: var(--common_fg_color);
  z-index: 1000;

  .reset-button,
  .apply-button {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    outline: none;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .reset-button {
    background-color: rgba(83, 88, 97, 0.1);
    color: var(--common_level1_base_color);
    margin-right: 10px;

    &:hover {
      background-color: rgba(83, 88, 97, 0.1);
    }
  }

  .apply-button {
    background-color: #111111;
    color: var(--common_white1_color);

    &:hover {
      background-color: #111111;
    }
  }
}

// 订阅频率区域
.category-frequency {
  border-left: 1px solid var(--common_overlay_normal_color);
}

.category-title,
.frequency-title {
  .title-base();
}

.frequency-title {
  margin-bottom: 2px;
  margin-top: 11px;
}

// 频率tab切换器
.frequency-tabs {
  display: flex;
  flex-wrap: wrap;
  padding: 0 7px;
  margin-bottom: 11px;

  .frequency-tab {
    .option-base();
    width: calc(33.33% - 10px);
  }
}

// 频率选择器
.frequency-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 12px;

  .selector-item {
    position: relative;

    // 钉钉Select组件适配样式
    .dingtalk-select {
      width: 100% !important;

      .dingtalk-select-selector {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid var(--button_secondary_line_color);
        background-color: var(--button_secondary_bg_color);
        font-size: 14px;
        color: var(--common_level1_base_color);
        transition: all 0.2s ease;
      }

      &:hover .dingtalk-select-selector {
        border-color: var(--common_level3_base_color);
      }

      &.dingtalk-select-focused .dingtalk-select-selector {
        border-color: #FF0E53;
        box-shadow: 0 0 0 2px rgba(255, 14, 83, 0.1);
      }

      &.dingtalk-select-disabled .dingtalk-select-selector {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

// 移除原有的自定义下拉菜单样式
.selector-dropdown,
.dropdown-arrow,
.dropdown-options,
.dropdown-option {
  display: none;
}



// 响应式设计
@media screen and (max-width: 375px) {
  .option-item,
  .frequency-tab {
    width: calc(50% - 10px);
  }
}

// hover效果（仅在支持hover的设备上）
@media (hover: hover) {
  .option-item,
  .frequency-tab {
    &:hover {
      border-color: #FF0E53;
      color: #FF0E53;
    }
  }
}
