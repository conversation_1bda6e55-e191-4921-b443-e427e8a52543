import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect } from 'react';
import { Select } from 'dingtalk-design-desktop';
import { setPageTitle } from '@/utils/jsapi';
import { FrequencyType, DayType, FrequencyConfig } from '@/common/types';
import Loading from '@/components/Loading';

import './index.less';

// 定义类型接口
interface CategoryOption {
  id: number | string;
  name: string;
  level: number;
  childrens?: CategoryOption[];
}

interface CategorySelectorProps {
  options: CategoryOption[];
  selectedOptions: CategoryOption[];
  frequencyConfig: FrequencyConfig;
  setFrequencyConfig: (config: FrequencyConfig) => void;
  onSelect: (option: CategoryOption) => void;
  onClose: () => void;
  onApply: () => void;
  loading?: boolean;
  categoryType: string;
}

const numMap = {
  0: i18next.t('j-agent-web_pages_category_components_CategorySelect_LevelCategory'),
  1: i18next.t('j-agent-web_pages_category_components_CategorySelect_SecondaryCategory'),
  2: i18next.t('j-agent-web_pages_category_components_CategorySelect_LevelCategory_1'),
};

// 频率tab数据
const frequencyTabs = [
  {
    key: 'daily' as FrequencyType,
    label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_DailyNewspaper'),
    defaultDayType: 'workday' as DayType,
    defaultTime: '10:00',
    defaultDataTypeIndex: 0,
    defaultTimeIndex: 20,
  },
  {
    key: 'weekly' as FrequencyType,
    label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_WeeklyNewspaper'),
    defaultDayType: 'friday' as DayType,
    defaultTime: '16:00',
  },
  {
    key: 'monthly' as FrequencyType,
    label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_MonthlyReport'),
    defaultDayType: 'first' as DayType,
    defaultTime: '10:00',
  }];

// 获取日期选项
const getDayOptions = (frequencyConfig: FrequencyConfig) => {
  if (frequencyConfig.type === 'daily') {
    return [
      { key: 'workday', index: 0, label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_EveryWorkingDay') },
      { key: 'everyday', index: 1, label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_EveryDay') }];
  } else if (frequencyConfig.type === 'weekly') {
    return [{ key: 'friday', index: 0, label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_EveryFriday') }];
  } else if (frequencyConfig.type === 'monthly') {
    return [{ key: 'first', index: 0, label: i18next.t('j-dingtalk-web_pages_category_CategorySelect_ThOfEachMonth') }];
  }
  return [];
};

// 生成时间选项（00:00 到 23:30，每30分钟一个）
const generateTimeOptions = () => {
  const options = [];
  let index = 0;
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push({
        index,
        value: timeString,
      });
      index++;
    }
  }
  return options;
};

const CategorySelector: React.FC<CategorySelectorProps> = ({
  options,
  selectedOptions,
  frequencyConfig,
  setFrequencyConfig,
  onSelect,
  onClose,
  onApply,
  loading = false,
  categoryType,
}) => {
  const timeOptions = generateTimeOptions();
  const dayOptions = getDayOptions(frequencyConfig);

  useEffect(() => {
    setPageTitle(i18next.t('j-agent-web_pages_category_components_CategorySelect_AllCategories'));
  }, []);

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleOptionClick = (option: CategoryOption) => {
    if (onSelect) {
      onSelect(option);
    }
  };

  // 处理频率类型切换
  const handleFrequencyTypeChange = (type: FrequencyType) => {
    const tabData = frequencyTabs.find((tab) => tab.key === type);
    if (!tabData) return;

    const newConfig = {
      type,
      dayType: tabData.defaultDayType,
      time: tabData.defaultTime,
      dataTypeIndex: tabData.defaultDataTypeIndex,
      timeIndex: tabData.defaultTimeIndex,
    };

    setFrequencyConfig(newConfig);
  };

  // 处理日期类型切换
  const handleDayTypeChange = (dayType: DayType) => {
    const dataTypeIndex = dayOptions.find((option) => option.key === dayType)?.index;
    setFrequencyConfig({ ...frequencyConfig, dayType, dataTypeIndex });
  };

  // 处理时间切换
  const handleTimeChange = (time: string) => {
    const timeIndex = timeOptions.find((option) => option.value === time)?.index;
    setFrequencyConfig({ ...frequencyConfig, time, timeIndex });
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return <Loading text={i18next.t('j-dingtalk-web_pages_category_CategorySelect_Loading')} />;
  }

  // 如果没有类目数据，显示空状态
  if (!options || options.length === 0) {
    return (
      <div className="category-selector no-data">
        {i18next.t(
          'j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable',
        )}
      </div>);
  }

  return (
    <div className="category-selector">
      <div className="category-selector-content">
        <div className="options-container">
          {options?.map((_c, i) => {
            return (
              <>
                <div className={`category-level${i}`}>{numMap[i]}</div>
                <div className="options-list">
                  {_c.childrens?.map((option) => {
                    return (
                      <div
                        key={option.id}
                        className={`option-item ${
                          selectedOptions.find((item) => item.id === option.id) ? 'selected' : ''}`
                        }
                        onClick={() => handleOptionClick(option)}
                      >


                        {option.name}
                      </div>);
                  })}
                </div>
              </>);
          })}
        </div>
        {
          categoryType === 'subscription' && (
            <div className="category-frequency">
              <div className="frequency-title">{i18next.t('j-dingtalk-web_pages_category_CategorySelect_SubscriptionSettings')}</div>
              {/* 频率类型选择器 */}
              <div className="frequency-tabs">
                {frequencyTabs.map((tab) => (
                  <div
                    key={tab.key}
                    className={`frequency-tab ${frequencyConfig.type === tab.key ? 'active' : ''}`}
                    onClick={() => handleFrequencyTypeChange(tab.key)}
                  >
                    {tab.label}
                  </div>
                ))}
              </div>

              {/* 日期选择器 */}
              <div className="frequency-selector">
                <div className="selector-item">
                  <Select
                    value={frequencyConfig.dayType}
                    onChange={(value) => handleDayTypeChange(value as DayType)}
                    disabled={frequencyConfig.type !== 'daily'}
                    placeholder={i18next.t('j-dingtalk-web_pages_category_CategorySelect_SelectDate')}
                    style={{ width: '100%' }}
                  >

                    {dayOptions?.map((option) => (
                      <Select.Option key={option.key} value={option.key}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </div>

                {/* 时间选择器 */}
                <div className="selector-item">
                  <Select
                    value={frequencyConfig.time}
                    onChange={(value) => handleTimeChange(value)}
                    disabled={frequencyConfig.type !== 'daily'}
                    placeholder={i18next.t('j-dingtalk-web_pages_category_CategorySelect_SelectTime')}
                    style={{ width: '100%' }}
                    showSearch={false}
                  >

                    {timeOptions?.map((option) => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.value}
                      </Select.Option>
                    ))}
                  </Select>
                </div>
              </div>
            </div>
          )
        }
      </div>

      {/* 底部按钮区域 */}
      <div className="action-buttons">
        <button className="reset-button" onClick={handleClose} type="button">{i18next.t('j-dingtalk-web_pages_category_CategorySelect_Cancel')}

        </button>
        <button className="apply-button" onClick={onApply} type="button">{i18next.t('j-dingtalk-web_pages_category_CategorySelect_Confirm')}

        </button>
      </div>
    </div>
  );
};

export default CategorySelector;
