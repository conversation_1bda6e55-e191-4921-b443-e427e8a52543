import { i18next } from '@ali/dingtalk-i18n';
import { useEffect, useState, useRef } from 'react';
import { cloneDeep } from 'lodash';
import quitWebview from '@ali/dingtalk-jsapi/api/biz/navigation/quit';
import closeWebview from '@ali/dingtalk-jsapi/api/biz/navigation/close';
import $Toast from '@ali/dingtalk-jsapi/api/device/notification/toast';
import { Toast } from 'dingtalk-design-mobile';
import { getUrlParam, getDecodedUrlParam } from '@/utils/util';
import { closePanel } from '@/utils/jsapi';
import { log } from '@/utils/console';
import { sendUT } from '@/utils/trace';
import { EPlatform, FrequencyConfig, DayType } from '@/common/types';
import {
  getCategoryList,
  getMessageParames,
  sendCopilotMsgByUser,
  switchReport,
  queryCategoryPath,
} from '@/apis';
import CategorySelector from './CategorySelect';

import './index.less';

// 定义类型接口
interface CategoryOption {
  id: number | string;
  name: string;
  level: number;
  childrens?: CategoryOption[];
}

// 类目层级映射
const levelMap = {
  zozo: 2,
  amazon: 3,
  rakuten: 2,
  default: 1,
};

// 获取平台最大层级
const getPlatformMaxLevel = (platformType: string): number => {
  switch (platformType) {
    case EPlatform.ZOZOTOWN:
      return levelMap.zozo;
    case EPlatform.AMAZON:
      return levelMap.amazon;
    case EPlatform.RAKUTEN:
      return levelMap.rakuten;
    default:
      return levelMap.default;
  }
};

// 判断是否达到最大层级
const isMaxLevelReached = (platformType: string, currentLevel: number): boolean => {
  return currentLevel === getPlatformMaxLevel(platformType);
};

// 判断是否达到最大数据
const hasReachedPlatformMaxData = (platformType: string, dataLength: number): boolean => {
  return dataLength === getPlatformMaxLevel(platformType);
};

// 根据时间获取时间索引
const getTimeIndex = (triggerTime: string): number => {
  /**
   * 00:00 到 23:30，每30分钟一个
   * 00:00 到 23:30 一共 48 个时间点
   */
  const [hours, minutes] = triggerTime.split(':');
  return +hours * 2 + +minutes / 30;
};

// 根据报告类型获取默认频率配置
const getDefaultFrequencyConfig = (reportType: string): FrequencyConfig => {
  const frequency = getUrlParam('frequency') as DayType || 'workday';
  const triggerTime = getUrlParam('triggerTime') as string || '10:00';
  const dataTypeIndex = frequency === 'everyday' ? 1 : 0;
  const timeIndex = getTimeIndex(triggerTime);
  switch (reportType) {
    case 'daily':
      return {
        type: 'daily',
        dayType: frequency,
        time: triggerTime,
        dataTypeIndex,
        timeIndex,
      };
    case 'weekly':
      return {
        type: 'weekly',
        dayType: 'friday',
        time: '16:00',
      };
    case 'monthly':
      return {
        type: 'monthly',
        dayType: 'first',
        time: '10:00',
      };
    default:
      return {
        type: 'daily',
        dayType: 'workday',
        time: '10:00',
        dataTypeIndex: 0,
        timeIndex: 20,
      };
  }
};

const defaultFrequencyConfig: FrequencyConfig = getDefaultFrequencyConfig(
  getUrlParam('reportType') as string || 'daily',
);

// 默认选中选项
const defaultSelectedOptions = [{
  name: i18next.t('j-agent-web_pages_category_LevelCategory'),
  id: '-1',
  level: 0,
}];

function App() {
  const { platform = '', reportType = 'daily', agentCode = '', corpId = '', categoryId = '', categoryType } = getUrlParam() as Record<string, string>;
  const categoryWithMsgId = getDecodedUrlParam('categoryWithMsgId');
  const [selectedOptions, setSelectedOptions] = useState<CategoryOption[]>(defaultSelectedOptions);
  const [categoryData, setCategoryData] = useState<CategoryOption[]>(defaultSelectedOptions);
  // 订阅频率状态
  const [frequencyConfig, setFrequencyConfig] = useState<FrequencyConfig>(defaultFrequencyConfig);
  const [loading, setLoading] = useState<boolean>(true);
  const isInit = useRef(false);
  const hasLoadedDefaultValue = useRef(false);

  // 初始化，拉取第一级分类数据
  useEffect(() => {
    // 页面曝光
    sendUT('category_page_view', {
      platform,
      reportType,
      agentCode,
      corpId,
    });

    getCategoryList({
      categoryId: -1,
      platform,
    })
      .then((res) => {
        const categoryDataCopy = [
          {
            name: i18next.t('j-agent-web_pages_category_LevelCategory'),
            level: 0,
            id: -1,
            childrens: [
              // 只有当 categoryType === 'subscription' 时才添加"综合"类目
              ...(categoryType === 'subscription' ? [{
                name: i18next.t('j-dingtalk-web_pages_category_Comprehensive'),
                level: 1,
                id: 99999,
              }] : []),
              ...res?.childrens?.map((item: any) => {
                return {
                  name: item.name,
                  level: 1,
                  id: item.categoryId,
                };
              })],

          }];

        setCategoryData(categoryDataCopy);
        isInit.current = true;
        // 如果没有需要加载默认值，直接隐藏loading
        if (!categoryId || categoryType !== 'subscription') {
          setLoading(false);
        }
      })
      .catch((err: any) => {
        log.error(err);
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_category_FailedToObtainCategoryData'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        setLoading(false);
      });
  }, []);

  // 选中的默认值拉取
  useEffect(() => {
    if (hasReachedPlatformMaxData(platform, categoryData?.length) || !isInit.current) {
      return;
    }

    // 如果已经加载过默认值，不再重复执行
    if (hasLoadedDefaultValue.current) {
      return;
    }

    // 只有在初始化且有 categoryId 参数时才执行默认值拉取逻辑
    // 避免在手动选择类目时重新设置选中状态
    if (categoryId && categoryType === 'subscription') {
      hasLoadedDefaultValue.current = true;

      // 如果 categoryId 是 99999（综合类目），直接选中综合类目
      if (categoryId === '99999') {
        const comprehensiveOption = {
          name: i18next.t('j-dingtalk-web_pages_category_Comprehensive'),
          id: 99999,
          level: 1,
        };
        const copy = [...selectedOptions];
        copy[1] = comprehensiveOption;
        setSelectedOptions(copy);
        setLoading(false);
        return;
      }

      queryCategoryPath({
        categoryId,
        platform,
      }).then((categoryPath: any) => {
        // Sort categoryIdPath by level (ascending order: 1, 2, 3...)
        const sortedCategoryPath = categoryPath?.categoryIdPath?.sort(
          (a: any, b: any) => a.level - b.level,
        );

        const categoryLevel = sortedCategoryPath?.map((item: any) => ({
          name: item.categoryJpName || item.categoryCnName,
          id: item.id,
          level: item.level,
        }));

        // Set selected options
        const copy = [...selectedOptions];
        for (let i = 0; i < categoryLevel.length; i++) {
          copy[categoryLevel[i].level] = categoryLevel[i];
        }
        setSelectedOptions(copy);

        // Function to recursively load category data for each level
        const loadCategoryDataRecursively = async (
          levelIndex: number,
          currentCategoryData: CategoryOption[],
        ) => {
          if (levelIndex >= categoryLevel.length) {
            // All levels processed, update state and mark as initialized
            setCategoryData(currentCategoryData);
            isInit.current = true;
            setLoading(false);
            return;
          }

          const option = categoryLevel[levelIndex];

          try {
            const res = await getCategoryList({
              categoryId: option.id,
              name: option.name,
              level: option.level,
              platform,
            });

            if (res?.childrens?.length > 0) {
              const categoryDataCopy = cloneDeep(currentCategoryData);
              categoryDataCopy[option.level] = {
                ...option,
                childrens: res.childrens?.map((item: any) => {
                  return {
                    ...item,
                    id: item.categoryId,
                  };
                }),
              };

              // Continue to next level
              await loadCategoryDataRecursively(levelIndex + 1, categoryDataCopy);
            } else {
              // No children, update state and stop processing
              setCategoryData(currentCategoryData);
              isInit.current = true;
              setLoading(false);
            }
          } catch (err: any) {
            log.error(err);
            Toast.fail({
              content: i18next.t('j-dingtalk-web_pages_category_FailedToObtainCategoryData'),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
            isInit.current = true; // Mark as initialized even on error to prevent infinite loop
            setLoading(false);
          }
        };

        // Start loading from level 1 (first item in sorted array should be level 1)
        if (categoryLevel?.length > 0) {
          loadCategoryDataRecursively(0, categoryData);
        } else {
          // 如果没有categoryLevel数据，直接隐藏loading
          setLoading(false);
        }
      })
        .catch((err: any) => {
          log.error(err);
          Toast.fail({
            content: i18next.t('j-dingtalk-web_pages_category_FailedToObtainCategoryData'),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
          setLoading(false);
        });
    }
  }, [categoryData]);

  // 选中分类
  const handleSelect = (option: CategoryOption) => {
    const copy = [...selectedOptions];

    // 找到相同 level 的元素并替换
    const existingIndex = copy.findIndex((item) => item.level === option.level);
    if (existingIndex !== -1) {
      copy[existingIndex] = option;
    } else {
      // 如果没有找到相同 level 的元素，按 level 添加到对应位置
      copy[option.level] = option;
    }

    // 如果选中的是"综合"类目，不需要请求子类目
    if (option.id === 99999) {
      // 清理当前选中层级之后的所有选项
      copy.splice(option.level + 1, selectedOptions.length - option.level - 1);

      // 清理 categoryData 中当前选中层级之后的分类数据
      // 只保留一级分类（索引0），移除所有二级、三级分类
      const categoryDataCopy = [categoryData[0]];

      setSelectedOptions(copy);
      setCategoryData(categoryDataCopy);
      return;
    }

    if (isMaxLevelReached(platform, option.level)) {
      // 如果当前级别是最后一级了则不需要请求数据了
      setSelectedOptions(copy);
      return;
    }

    if (option.level < selectedOptions.length) {
      // 如果当前选中层级比已选层级小，则删除当前选中层级后面的所有选项
      copy.splice(option.level + 1, selectedOptions.length - option.level - 1);
      setSelectedOptions(copy);
    }

    const categoryDataCopy = cloneDeep(categoryData);

    // 清理 categoryData 中当前选中层级之后的分类数据
    // 只保留到当前选中层级的数据，避免使用 delete 造成 undefined 元素
    const cleanedCategoryData = categoryDataCopy.slice(0, option.level + 1);

    getCategoryList({
      categoryId: option.id,
      name: option.name,
      level: option.level,
      platform,
    })
      .then((res) => {
        cleanedCategoryData[option.level] = {
          ...option,
          childrens: res.childrens?.map((item: any) => {
            return {
              ...item,
              id: item.categoryId,
            };
          }),
        };
        copy[option.level] = option;
        setSelectedOptions(copy);
        setCategoryData(cleanedCategoryData);
      })
      .catch((err: any) => {
        log.error(err);
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_category_FailedToObtainCategoryData'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      });
  };

  // 关闭选择面板
  const handleClose = () => {
    closePanel();
  };

  // 提交类目选择
  const handleApply = () => {
    const selectedCategory = selectedOptions?.filter((item) => item.level > 0);

    // 如果选择的是"综合"类目，跳过规则判断
    const isComprehensiveCategory = selectedCategory.some((item) => +item.id === 99999);

    if (!isComprehensiveCategory) {
      // 如果平台是 zozotown，则只需要选择一级类目，其他平台需要选择最大层级
      const requiredLevel = platform === EPlatform.ZOZOTOWN && categoryType === 'subscription' ? 1 : getPlatformMaxLevel(platform);

      // Safe way to get max level
      const maxSelectedLevel = selectedCategory.length > 0 ?
        Math.max(...selectedCategory.map((item) => item.level)) :
        0;

      if (
        !selectedCategory ||
      selectedCategory.length === 0 ||
      maxSelectedLevel < requiredLevel) {
        let toastMessage = i18next.t('j-dingtalk-web_pages_category_PleaseSelectALevelCategory');

        // 针对Amazon平台的具体提示
        if (platform === EPlatform.AMAZON || categoryType !== 'subscription') {
          if (maxSelectedLevel === 0 || selectedCategory.length === 0) {
            toastMessage = i18next.t('j-dingtalk-web_pages_category_PleaseSelectALevelCategory');
          } else if (maxSelectedLevel === 1) {
            toastMessage = i18next.t('j-dingtalk-web_pages_category_SelectASecondaryCategory');
          } else if (maxSelectedLevel === 2) {
            toastMessage = i18next.t('j-dingtalk-web_pages_category_SelectALevelCategory');
          }
        }

        Toast.fail({
          content: toastMessage,
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        return;
      }
    }

    // 这里可以添加实际的应用逻辑，如发送API请求等
    if (categoryType === 'subscription') {
      switchReport({
        categoryIdPath: selectedCategory,
        agentCode,
        corpId,
        reportType,
        platform,
        bizId: getDecodedUrlParam('bizId'),
        sessionId: getDecodedUrlParam('sessionId'),
        reportTypes: frequencyConfig.type,
        frequency: JSON.stringify({
          index: frequencyConfig.dataTypeIndex,
          value: frequencyConfig.dayType,
        }),
        triggerTime: JSON.stringify({
          index: frequencyConfig.timeIndex,
          value: frequencyConfig.time || '10:00',
        }),
      }).then((res: any) => {
        log('res: ', res);
        // 订阅成功
        sendUT('category_subscribe_success', {
          platform,
          reportType,
          agentCode,
          corpId,
          categoryIdPath: selectedCategory,
          frequencyConfig,
          reportTypes: frequencyConfig.type,
          frequency: JSON.stringify({
            index: frequencyConfig.dataTypeIndex,
            value: frequencyConfig.dayType,
          }),
          triggerTime: JSON.stringify({
            index: frequencyConfig.timeIndex,
            value: frequencyConfig.time || '10:00',
          }),
        });

        $Toast({
          icon: 'success', // 提示图标,
          text: i18next.t('j-dingtalk-web_pages_category_CategorySelectionSucceeded'), // 提示信息
          duration: 2,
        }).then(() => {
          // PC 端使用
          quitWebview({
            success() {},
            fail() {},
          });
          setTimeout(() => {
            // Mobile 端使用
            closeWebview({
              success() {},
              fail() {},
            });
          }, 2000);
        });
      })
        .catch((err: any) => {
          log.error(err);

          // 发送失败埋点
          sendUT('category_subscribe_failed', {
            platform,
            reportType,
            agentCode,
            corpId,
            categoryIdPath: selectedCategory,
            frequencyConfig,
            error: err?.message || 'Unknown error',
          });

          $Toast({
            icon: 'error', // 提示图标,
            text: err?.message || i18next.t('j-dingtalk-web_pages_category_CategorySelectionFailed'),
            duration: 2,
          });
        });
    } else if (categoryWithMsgId) {
      getMessageParames({ categoryWithMsgId }).then((res) => {
        if (res?.result?.[0]?.content?.textContent?.text) {
          // 寻找 level 最大的类目名称
          const maxLevelName = selectedOptions?.reduce((max, current) => {
            return current.level > max.level ? current : max;
          }, { level: 0, name: '' })?.name;
          res.result[0].content.textContent.text =
          res?.result?.[0]?.content?.textContent?.text?.replace(
            '{{category}}',
            maxLevelName,
          );
        }
        sendCopilotMsgByUser(res.result).then(() => {
          // 消息重新发送成功后关闭当前页面
          closePanel();
        });
      }).catch((err: any) => {
        log.error(err);
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_category_CategorySwitchingFailedPleaseTry'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      });
    }
  };

  return (
    <div className="app">
      <div className="demo-container">
        <CategorySelector
          options={categoryData}
          selectedOptions={selectedOptions}
          frequencyConfig={frequencyConfig}
          setFrequencyConfig={setFrequencyConfig}
          loading={loading}
          categoryType={categoryType}
          onSelect={handleSelect}
          onClose={handleClose}
          onApply={handleApply}
        />
      </div>
    </div>
  );
}

export default App;
