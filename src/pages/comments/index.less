@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.j-commodity-comments {
  margin-top: 24px;
  margin-bottom: 24px;

  .comments-list {
    margin: 0 16px;

    .comments-rates {
      --star-size: 16px;
      --active-color: @theme_danger1_color;
      margin-bottom: 8px;
    }

    .comments-item {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
      margin-bottom: 24px;

      .comments-title {
        .common_subhead_bold_text_style_mob();
        color: @common_level1_base_color;
      }

      .comments-content {
        .common_action_text_style_mob();
        color: @common_level3_base_color;
        margin-top: 8px;
      }

      .comments-date {
        .common_footnote_text_style_mob();
        color: @common_level4_base_color;
        margin-top: 8px;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }
  }

  // Custom scrollbar styles for better appearance
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: @common_level4_base_color;
    border-radius: @common_border_radius_s;
    transition: background @common_light_motion_duration @common_light_motion_timing_function;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: @common_level3_base_color;
  }

  // Dark mode scrollbar
  :root[data-dingtalk-theme='dark'] & {
    &::-webkit-scrollbar-thumb {
      background: @common_white4_color;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: @common_white3_color;
    }
  }
}

.amazon-summary {
  margin: 24px 16px;
  &-title {
    .common_action_bold_text_style_mob();
    color: @common_level1_base_color;
    margin-bottom: 4px;
  }
  &-desc {
    .common_footnote_text_style_mob();
    color: @common_level2_base_color;
    margin-bottom: 12px;
  }
  &-detail {
    .common_action_text_style_mob();
    color: @common_level1_base_color;
  }
}
