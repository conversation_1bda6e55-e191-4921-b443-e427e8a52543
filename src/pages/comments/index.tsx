import React from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { EPlatform, IComment } from '@/common/types';
import { formatDateTime } from '@/utils/util';
import { Rate } from 'dingtalk-design-mobile';

import './index.less';

function Comments({ className = '', commentsData, platform }: { className?: string; commentsData: any; platform: EPlatform }) {
  const data = commentsData?.commentDetails || [];

  if (platform === EPlatform.AMAZON) {
    if (commentsData?.summary?.length === 0) {
      return null;
    }

    return (
      <div className="amazon-summary">
        <div className="amazon-summary-title">
          {i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback')}
        </div>
        <div className="amazon-summary-desc">
          {i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn')}
        </div>
        <div className="amazon-summary-detail">
          {commentsData?.summary}
        </div>
      </div>
    );
  }

  return (
    <div className={`j-commodity-comments ${className}`}>
      <div className="comments-list">
        {data?.map((item: IComment) => (
          <React.Fragment key={item.title}>
            <Rate
              className="comments-rates"
              readOnly
              value={item.start}
              allowHalf
              warningDesc={i18next.t('j-agent-web_pages_comments_CurrentlyScoringIsNotSupported')}
            />
            <div className="comments-item">
              <div className="comments-title">{item.title}</div>
              <div className="comments-content">{item.content}</div>
              <div className="comments-date">{formatDateTime(item.time)}</div>
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

export default Comments;
