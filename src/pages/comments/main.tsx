import React, { useEffect } from 'react';
import { getProductComment } from '@/apis';
import ZozoComments from '@/pages/commodityDetail/components/ZozoComments';
import AmazonComments from '@/pages/commodityDetail/components/AmazonComments';
import { EPlatform } from '@/common/types';
import { sendUT } from '@/utils/trace';
import { getUrlParam } from '@/utils/util';
import Comments from './index';
import './main.less';

function Main() {
  const [commentsData, setCommentsData] = React.useState(null);
  const productId = getUrlParam('productId');
  const sourceItemId = getUrlParam('sourceItemId');
  const platform = getUrlParam('platform') as EPlatform;
  useEffect(() => {
    // 页面曝光
    sendUT('comments_page_view', {
      productId,
      platform,
    });

    getProductComment({
      productId,
      sourceItemId,
      platform,
    }).then((res) => {
      setCommentsData(res);
    });
  }, []);

  return (
    <div className="comments-page">
      {
        platform === EPlatform.AMAZON ? (
          <AmazonComments
            data={{
              commentReport: commentsData,
            }}
            platform={platform}
            showMore
          />
        ) : (
          <ZozoComments
            data={{
              commentReport: commentsData,
            }}
            platform={platform}
            showTitle={false}
          />
        )
      }
      <Comments
        commentsData={commentsData}
        platform={platform}
      />
    </div>
  );
}

export default Main;
