.amazon-comments {
  background-color: var(--common_bg_z0_color);
  border-radius: 12px;
  padding: 8px;
  margin-top: 12px;
  cursor: pointer;
  &.more {
    margin-left: 16px;
    margin-right: 16px;
    cursor: default;
  }

  &-header {
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  &-score {
    display: flex;
    align-items: center;
  }
  &-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: var(--common_level1_base_color);
  }
  &-rates {
    --star-size: 16px !important;
    --active-color: #FF0E53 !important;
    margin-left: 8px;
  }
  &-more {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    line-height: 18px;
    color: var(--common_level2_base_color);
  }
  &-description {
    font-size: 12px;
    line-height: 18px;
    color: var(--common_level2_base_color);
    margin-top: 4px;
  }
  &-divider {
    height: 0.5px;
    background-color: var(--common_line_light_color);
    margin: 8px 0;
  }
  &-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  &-feature {
    display: flex;
    align-items: center;
    padding: 6px 8px 6px 6px;
    background-color: var(--common_overlay_area_color);
    border-radius: 4px;
    color: var(--common_level1_base_color);
    font-size: 12px;
    line-height: 18px;

    .ding-icon {
      font-size: 12px;
      margin-right: 4px;
      color: var(--common_level1_base_color);
    }

    span {
      white-space: nowrap;
    }
  }
}
