/* eslint-disable max-len */
import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { AmazonCommentsProps } from '@/common/types';
import { CheckOutlined, RightArrowOutlined } from '@ali/ding-icons';
import { isPCDingTalk } from '@ali/dingtalk-jsapi/plugin/environmentV2';
import RateItemWithPercent from '@/components/RateItemWithPercent';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { Rate } from 'dingtalk-design-mobile';
import { openLink } from '@/utils/jsapi';
import { getNdingDomain } from '@/utils/env';
import './index.less';

const AmazonComments: React.FC<AmazonCommentsProps> = ({
  data,
  showMore = true,
  setDrawerVisible,
  pageType,
  platform,
}) => {
  // 数据预处理
  const commentReport = data?.commentReport || {};
  const _tags = data?.commentReport?.tags;
  const tags = _tags?.length > 0 ? _tags?.split(',')?.map((tag) => tag.trim()) : [];

  // 将 scoreDetails 对象转换为数组并按评分从高到低排序
  const scoreDetailsArray = commentReport?.scoreDetails
    ? Object.entries(commentReport.scoreDetails)
      .map(([rating, percentage]) => ({
        feature: i18next.t('j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating', { rating: parseInt(rating, 10) }),
        percentage: parseInt(String(percentage).replace('%', ''), 10),
        rating: parseInt(rating, 10),
      }))
      .sort((a, b) => b.rating - a.rating)
    : [];

  const handleClick = () => {
    if (pageType === 'RankingList') {
      const url = `${getNdingDomain()}/dingding/J-dingtalk/comments/index.html?productId=${data?.productId}&sourceItemId=${data?.sourceItemId}&platform=${platform}&title=${i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails')}`;

      openLink(url);
    } else {
      $openLink({ url: data?.detailHtml });
    }
  };

  if (commentReport?.total === 0) {
    return null;
  }

  return (
    <div
      className={`amazon-comments ${showMore ? 'more' : ''}`}
      onClick={() => {
        if (showMore) {
          return;
        }

        isPCDingTalk() ? setDrawerVisible(true, data?.productId, data?.sourceItemId) : handleClick();
      }}
    >
      <>
        <div className="amazon-comments-header">
          <div className="amazon-comments-header-top">
            <div className="amazon-comments-score">
              <span className="amazon-comments-title">
                {i18next.t('j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore', { score: commentReport?.avgScore })}
              </span>
              <Rate
                className="amazon-comments-rates"
                readOnly
                value={commentReport?.avgScore}
                allowHalf
                warningDesc={i18next.t('j-agent-web_pages_comments_CurrentlyScoringIsNotSupported')}
              />
            </div>
            {
              !showMore && (
                <div className="amazon-comments-more">
                  {i18next.t('j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails')}
                  <RightArrowOutlined />
                </div>
              )
            }
          </div>
          {
            commentReport?.summary?.length > 0 && (
              <div className="amazon-comments-description">
                {i18next.t('j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount', { commentCount: data?.commentReport?.total })}
              </div>
            )
          }
          {
            scoreDetailsArray?.length > 0 && showMore && (
              <div className="amazon-comments-score-details">
                {scoreDetailsArray?.map((item: any) => (
                  <RateItemWithPercent key={item.feature} item={item} />
                ))}
              </div>
            )
          }
        </div>
        {
          tags?.length > 0 && <div className="amazon-comments-divider" />
        }
        {
          tags?.length > 0 && (
            <div className="amazon-comments-features">
              {
                tags?.map((tag: string) => (
                  <div className="amazon-comments-feature" key={tag}>
                    <CheckOutlined />
                    <span>{tag?.trim()}</span>
                  </div>
                ))
              }
            </div>
          )
        }
      </>
    </div>
  );
};

export default AmazonComments;
