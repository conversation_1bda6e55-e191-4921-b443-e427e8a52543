import React, { useRef, useEffect } from 'react';
import { Product } from '@/common/types';
import { Tabs } from 'dingtalk-design-mobile';
import OptimizedImage from '@/components/OptimizedImage';
import './style.less';

interface ProductGalleryProps {
  products: Product[];
  activeIndex: number;
  loading: boolean;
  onProductClick: (index: number) => void;
  listKeyChange?: (key: string) => void;
  listKey?: string | null;
  pageType?: string;
  moreTabs?: any[];
}

const ProductGallery: React.FC<ProductGalleryProps> = ({
  products,
  loading,
  activeIndex,
  onProductClick,
  listKeyChange,
  listKey,
  pageType,
  moreTabs,
}) => {
  const galleryRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Array<HTMLDivElement | null>>([]);
  // 初始化缩略图元素引用数组
  useEffect(() => {
    itemRefs.current = itemRefs.current.slice(0, products.length);
  }, [products.length]);

  // 当activeIndex变化时，确保当前活动的缩略图在可视区域内
  useEffect(() => {
    if (galleryRef.current && itemRefs.current[activeIndex]) {
      const gallery = galleryRef.current;
      const activeItem = itemRefs.current[activeIndex] as HTMLElement;

      if (activeItem) {
        // 计算需要滚动的位置，使活动项居中
        const galleryWidth = gallery.offsetWidth;
        const itemWidth = activeItem.offsetWidth;
        const itemLeft = activeItem.offsetLeft;
        const scrollPosition = itemLeft - galleryWidth / 2 + itemWidth / 2;

        gallery.scrollTo({
          left: scrollPosition,
          behavior: 'smooth',
        });
      }
    }
  }, [activeIndex]);

  return (
    <div className="product-gallery-container">
      {
        (pageType === 'RankingList') && (
          <Tabs
            tabs={moreTabs}
            fullWidth={false}
            activeKey={listKey}
            onChange={listKeyChange}
            hideBottomDivider
          />
        )}
      {
        products.length > 0 && !loading &&
        (
          <div className="product-gallery" ref={galleryRef}>
            {products.map((product, index) => (
              <div
                key={product.productId}
                ref={(el) => { itemRefs.current[index] = el; }}
                className={`gallery-item ${index === activeIndex ? 'active' : ''}`}
                onClick={() => onProductClick(index)}
                data-index={index}
              >
                <OptimizedImage
                  src={product.images[0] || ''}
                  alt={product.title}
                  width="100%"
                  height="100%"
                  lazy
                  quality={85}
                />
              </div>
            ))}
          </div>
        )
      }
    </div>
  );
};

export default ProductGallery;
