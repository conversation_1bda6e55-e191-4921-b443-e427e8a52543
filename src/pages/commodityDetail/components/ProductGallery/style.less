.product-gallery-container {
  position: sticky;
  top: 0;
  z-index: 100;
}

.product-gallery {
  display: flex;
  overflow-x: auto;
  gap: 10px;
  padding: 10px;
  // background-color: #f8f8f8;
  background-color: var(--common_fg_color);
  scrollbar-width: none;
  // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &::-webkit-scrollbar {
    display: none;
  }

  .gallery-item {
    flex: 0 0 auto;
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
    position: relative;

    // 添加过渡效果，但只对边框应用
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 3px solid transparent;
      border-radius: 10px;
      transition: border-color 0.2s ease;
      pointer-events: none;
      z-index: 1;
    }

    // 优化图片显示，防止高度坍塌
    .optimized-image-container {
      width: 100%;
      height: 100%;
      display: block;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }
    }

    // 兼容原有的img标签
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    &.active::after {
      border-color: #ff0e53;
    }
  }
}
