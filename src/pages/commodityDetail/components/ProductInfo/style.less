.product-info {
  padding: 8px 0 24px 0;

  .product-info-container {
    &:hover {
      cursor: pointer;
    }
  }
  
  .product-title {
    font-size: 16px;
    cursor: pointer;
    font-weight: bold;
    margin-bottom: 8px;
    word-break: break-word;
    color: var(--common_level1_base_color);
    line-height: 1.4;
  }

  .product-price-container {
    display: flex;
    cursor: pointer;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .price-section {
      display: flex;
      align-items: baseline;
      
      .price {
        color: #FF0E53;
        font-size: 24px;
        font-weight: bold;
        margin-right: 5px;
      }
      
      .original-price {
        color: #FF0E53;
        font-size: 16px;
      }

      .unit {
        font-weight: 600;
        font-size: 14px;
      }
    }
    
    .profit-section {
      display: flex;
      align-items: center;
      background-color: #fff8f0;
      padding: 3px 8px;
      border-radius: 4px;
      
      .profit-label {
        font-size: 12px;
        color: #666;
        margin-right: 5px;
      }
      
      .profit-value {
        color: #ff6600;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }

  .product-stats {
    display: flex;
    gap: 6px;
    color: #666;
    margin-bottom: 12px;
    .tag .value {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2px 4px;
      font-size: 12px;
      border: 0.5px solid #FF0E53;
      border-radius: 4px;
      color: var(--extended_red5_color, #FF0E53);
    }
  }

  .product-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:hover {
      cursor: pointer;
    }
    .store-name {
      font-size: 14px;
      color: var(--common_level3_base_color);
    }
    .find-similar {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: var(--common_level3_base_color);
    }
  }
}
