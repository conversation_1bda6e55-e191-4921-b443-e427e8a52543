import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect } from 'react';
import { Product, EPlatform } from '@/common/types';
import { getProductList, getProductSelectionReportDetail, getProductSelectionDetail } from '@/apis';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { getDecodedUrlParam, getUrlParam } from '@/utils/util';
import { openLink, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import { getNdingDomain } from '@/utils/env';
import { generateProductTabs } from '@/utils/productRankings';
import ImageSwiper from '@/components/ImageSwiper';
import ProductGallery from '../ProductGallery';
import ProductInfo from '../ProductInfo';
import Skeleton from '../Skeleton';
import './style.less';

const ProductPage: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const productListRef = useRef<HTMLDivElement>(null);
  const productRefs = useRef<Array<HTMLDivElement | null>>([]);
  const [init, setInit] = useState(false);
  const [productData, setProductData] = useState<Product[]>([]);
  const pageType = getUrlParam('pageType') || 'RankingList';
  const reportType = getUrlParam('reportType') || 'daily';
  const _platform = getUrlParam('platform') as EPlatform || EPlatform.ZOZOTOWN;
  const bizId = getDecodedUrlParam('bizId');
  const [listKey, setListKey] = useState<string>(
    getUrlParam('listKey') || 'salesProductList',
  );
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [platform, setPlatform] = useState<EPlatform>(_platform);
  const [moreTabs, setMoreTabs] = useState<any[]>(generateProductTabs(reportType, _platform));

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts'));
    // 页面曝光
    sendUT('product_page_view', {
      reportType,
      agentCode: getUrlParam('agentCode'),
      corpId: getUrlParam('corpId'),
      listKey,
      pageType,
      bizId,
      productId: getUrlParam('productId'),
    });
    initData();
  }, []);

  useEffect(() => {
    if (platform && reportType && (platform !== _platform)) {
      setMoreTabs(generateProductTabs(reportType, platform));
    }
  }, [platform]);

  const initData = () => {
    setLoading(true);
    const productId = getUrlParam('productId') || '';

    let api = null;
    if (pageType === '1vN') {
      api = getProductSelectionDetail({
        bizId,
        rankName: listKey || 'salesProductList',
      });
    } else if (pageType === 'RankingList') {
      api = getProductSelectionReportDetail({
        bizId,
        rankName: listKey || 'salesProductList',
        reportType,
      });
    } else {
      api = getProductList({ bizId });
    }

    api
      .then((res) => {
        setInit(true);

        setTimeout(() => {
          setLoading(false);
          setPlatform(res.platform);
          setProductData(res.products);
          productRefs.current = productRefs.current.slice(0, res.products.length);
          if (productId) {
            const index = res.products.findIndex((item) => `${item.productId}` === productId);
            if (index !== -1) {
            // 先设置活动索引，再进行滚动
              setActiveIndex(index);
              setTimeout(() => {
                handleProductClick(index);
              }, 100);
            }
          }
        }, 1000);
      })
      .catch(() => {});
  };

  const tabChange = (key: string) => {
    getProductSelectionReportDetail({
      bizId,
      rankName: key,
      reportType,
    })
      .then((res) => {
        setLoading(true);
        setInit(true);
        setTimeout(() => {
          setLoading(false);
          setProductData(res.products);
          setActiveIndex(0);
          setTimeout(() => {
            handleProductClick(0);
          }, 100);
          if (!res.products || res.products.length === 0) {
            setProductData([]);
          }
        }, 1000);
      })
      .catch(() => {
        setProductData([]);
      });
  };

  // 处理商品缩略图点击
  const handleProductClick = (index: number): void => {
    // 设置导航状态为true，阻止滚动监听器更新activeIndex
    setIsNavigating(true);
    setActiveIndex(index);

    // 滚动到对应的商品详情
    if (productRefs.current[index]) {
      productRefs.current[index]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });

      // 设置一个定时器，在滚动完成后重新启用滚动监听
      setTimeout(() => {
        setIsNavigating(false);
      }, 1200); // 800毫秒通常足够完成滚动动画
    }
  };

  // 商品点击寻源
  const handleOriginClick = (product: Product) => {
    // 商品点击
    sendUT('product_detail_click', {
      platform,
      reportType,
      pageType,
      listKey,
      bizId,
      productId: product.productId,
      productTitle: encodeURIComponent(product.title),
    });

    if (pageType === 'RankingList' || pageType === '1vN') {
      const url = `${getNdingDomain()}/dingding/J-dingtalk/productOriginV2/index.html?platform=${platform}&reportType=${reportType}&productId=${product.productId}&listKey=${listKey}&url=${encodeURI(product.images?.[0])}&productTitle=${encodeURIComponent(product.title)}&price=${product.price}&bizId=${bizId}`;

      openLink(url);
    } else {
      $openLink({ url: product.detailHtml });
    }
  };

  // 监听滚动事件，更新当前活动的商品索引
  useEffect(() => {
    // 添加节流函数
    function throttle(fn: Function, delay: number) {
      let lastTime = 0;
      return function (...args: any[]) {
        const now = Date.now();
        if (now - lastTime >= delay) {
          fn.apply(this, args);
          lastTime = now;
        }
      };
    }

    const handleScroll = () => {
      // 如果正在通过导航进行滚动，不更新activeIndex
      if (isNavigating) return;

      // 获取吸顶导航的高度
      const galleryHeight = document.querySelector('.product-gallery-container')?.clientHeight || 0;
      const scrollPosition = window.scrollY + galleryHeight + 20; // 添加一些偏移量

      // 找出当前在视口中最顶部的商品
      let currentIndex = 0;
      let minDistance = Number.MAX_VALUE;

      productRefs.current.forEach((ref, index) => {
        if (ref) {
          const rect = ref.getBoundingClientRect();
          const offsetTop = rect.top + window.scrollY;
          const distance = Math.abs(scrollPosition - offsetTop);

          if (distance < minDistance) {
            minDistance = distance;
            currentIndex = index;
          }
        }
      });

      // 特殊处理最后一个商品：如果滚动接近底部，选择最后一个
      const scrollBottom = window.scrollY + window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      if (documentHeight - scrollBottom < 100) {
        // 当距离底部不到100px时
        currentIndex = productData.length - 1;
      }

      if (currentIndex !== activeIndex) {
        setActiveIndex(currentIndex);
      }
    };

    // 使用节流函数包装滚动处理函数，200ms 执行一次
    const throttledHandleScroll = throttle(handleScroll, 200);

    window.addEventListener('scroll', throttledHandleScroll);

    // 初始执行一次以设置正确的初始索引
    if (!isNavigating && productData.length > 0) {
      setTimeout(() => {
        handleScroll(); // 这里使用原始函数，确保初始化时立即执行
      }, 1000); // 给DOM足够的时间渲染
    }

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
    };
  }, [activeIndex, isNavigating, productData]); // 添加 productData 作为依赖项

  return (
    <div className="product-page-container">
      <div className="product-page">
        {/* 顶部商品缩略图列表 */}
        <ProductGallery
          loading={loading}
          products={productData}
          activeIndex={activeIndex}
          listKeyChange={(v) => {
            setListKey(v);
            tabChange(v);
          }}
          pageType={pageType}
          listKey={listKey}
          moreTabs={moreTabs}
          onProductClick={handleProductClick}
        />

        {loading ? <Skeleton /> : (
          <>
            {init && productData.length === 0 &&
            <div style={{ textAlign: 'center', marginTop: '200px' }}>
              <img
                style={{ width: '98px', height: '98px', margin: '0 auto' }}
                src="https://gw.alicdn.com/imgextra/i1/O1CN01AO4dL21oe60LxXtdE_!!6000000005249-2-tps-192-192.png"
                alt={i18next.t(
                  'j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee',
                )}
              />
              <div
                style={{
                  textAlign: 'center',
                  padding: '0 40px',
                  color: 'var(--common_level1_base_color)',
                }}
              >

                {i18next.t(
                  'j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct',
                )}
              </div>
            </div>
            }
            {/* 商品详情列表 */}
            {productData.length > 0 &&
            <>
              <div className="product-list" ref={productListRef}>
                {productData.map((product, index) =>
                  (
                    <div
                      key={product.productId}
                      className={`product-item${product.images.length === 1 ? ' no-node' : ''}`}
                      ref={(el) => {
                        productRefs.current[index] = el;
                      }}
                      data-index={index}
                    >
                      <ImageSwiper
                        images={product.images}
                        productId={`${product.productId}`}
                        onClick={() => handleOriginClick(product)}
                      />
                      <ProductInfo
                        product={product}
                        pageType={pageType}
                        platform={platform}
                        onClick={() => handleOriginClick(product)}
                      />
                    </div>
                  ))}
              </div>
              {init && <div className="product-list-bottom">—— {i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore')} ——</div>}
            </>
            }
          </>
        )}
      </div>
    </div>);
};

export default ProductPage;
