.product-page {
  display: flex;
  flex-direction: column;
  width: 100%;

  max-width: var(--product-max-width);
  min-width: 270px;
}

.product-page-container {
  display: flex;
  background-color: var(--common_fg_color);
  justify-content: center;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

.dtm-swiper-content {
  width: 100%;
  aspect-ratio: 1/1; // 固定宽高比防止高度坍塌
  max-height: 380px;
  overflow: hidden;
  background-color: var(--common_level7_base_color);

  // 优化图片容器
  .optimized-image-container {
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }
}

.product-list {
  display: flex;
  padding: 5px 16px 5px;
  flex-direction: column;
  width: 100%;
  padding-top: 5px; // 添加一点顶部间距，避免内容被吸顶导航遮挡
  .dtm-swiper-track-allow-touch-move {
    touch-action: pan-y pan-x;
  }
}

.no-node {
  .dtm-swiper-indicator {
    display: none;
  }
}

.product-list-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 24px;
  color: var(--common_level4_base_color);
}

.product-item {
  scroll-margin-top: 100px; // 设置滚动到此元素时的顶部边距，考虑吸顶导航的高度
}

.product-main-image-container {
  position: relative;
  width: 100%;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 5px;
}

.product-main-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: block;
  object-fit: cover;
}
