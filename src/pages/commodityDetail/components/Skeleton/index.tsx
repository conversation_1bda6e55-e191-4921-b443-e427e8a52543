import { Skeleton, WhiteSpace, WingBlank } from 'dingtalk-design-mobile';
import './index.less';

const SkeletonComponent: React.FC = () => {
  return (
    <div className="skeleton-container">
      <WingBlank>
        <WhiteSpace />
        <Skeleton type="card" animated width="100%" height={'380px'} />
        <WhiteSpace />
        <Skeleton type="text" width="100%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="60%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="40%" animated />

        <WhiteSpace />
        <Skeleton type="card" animated width="100%" height={'380px'} />
        <WhiteSpace />
        <Skeleton type="text" width="100%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="60%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="40%" animated />

        <WhiteSpace />
        <Skeleton type="card" animated width="100%" height={'380px'} />
        <WhiteSpace />
        <Skeleton type="text" width="100%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="60%" animated />
        <WhiteSpace />
        <Skeleton type="text" width="40%" animated />
      </WingBlank>
    </div>
  );
};

export default SkeletonComponent;
