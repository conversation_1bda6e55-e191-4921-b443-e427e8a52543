/* eslint-disable max-len */
import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import RateItemWithPercent from '@/components/RateItemWithPercent';
import { ZozoCommentsProps } from '@/common/types';
import { RightArrowOutlined } from '@ali/ding-icons';
import { isPCDingTalk } from '@ali/dingtalk-jsapi/plugin/environmentV2';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { openLink } from '@/utils/jsapi';
import { getNdingDomain } from '@/utils/env';
import './styles.less';

const ZozoComments: React.FC<ZozoCommentsProps> = ({
  data,
  showTitle = true,
  setDrawerVisible,
  pageType,
  platform,
}) => {
  const commentAnalyses = data?.commentReport?.commentAnalyses || data?.commentAnalyses || [];

  const handleClick = () => {
    if (pageType === 'RankingList') {
      const url = `${getNdingDomain()}/dingding/J-dingtalk/comments/index.html?productId=${data?.productId}&sourceItemId=${data?.sourceItemId}&platform=${platform}&title=${i18next.t('j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails')}`;

      openLink(url);
    } else {
      $openLink({ url: data?.detailHtml });
    }
  };

  if (!showTitle && commentAnalyses?.length === 0) {
    return null;
  }

  return (
    <div
      className={`customer-reviews ${!showTitle ? 'more' : ''}`}
      onClick={() => {
        if (!showTitle) {
          return;
        }

        isPCDingTalk() ? setDrawerVisible(true, data?.productId, data?.sourceItemId) : handleClick();
      }}
    >
      {showTitle && (
        <>
          <div
            className="customer-reviews-header"
            style={{ paddingBottom: commentAnalyses?.length > 0 ? '8px' : '0' }}
          >
            <div className="customer-reviews-title">
              {i18next.t(
                'j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc',
                { dataCommDesc: data?.commentReport?.total },
              )}
              <RightArrowOutlined className="customer-reviews-right-icon" />
            </div>
          </div>
          {
            commentAnalyses?.length > 0 && (
              <>
                <div className="customer-reviews-description">
                  {i18next.t(
                    'j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures',
                  )}
                  { commentAnalyses.map((item) => item.feature).join('、')}
                </div>
                <div className="customer-reviews-divider" />
              </>
            )
          }
        </>
      )}
      {
        commentAnalyses?.length > 0 && (
          <div className="customer-reviews-content">
            {commentAnalyses.map((item: any) => (
              <RateItemWithPercent key={item.feature} item={item} />
            ))}
          </div>
        )
      }
    </div>
  );
};

export default ZozoComments;
