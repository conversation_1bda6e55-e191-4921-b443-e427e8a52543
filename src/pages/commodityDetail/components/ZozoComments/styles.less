.customer-reviews {
  background-color: var(--common_bg_z0_color);
  border-radius: 12px;
  padding: 8px;
  margin-top: 12px;
  cursor: pointer;
  &.more {
    cursor: default;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .customer-reviews-title {
      display: flex;
      align-items: center;
      color: var(--common_level1_base_color);
      font-size: 14px;
      line-height: 22px;
      font-weight: 500;
    }

    .customer-reviews-right-icon {
      color: var(--common_level2_base_color);
      font-size: 14px;
      line-height: 22px;
    }
  }

  &-description {
    font-size: 12px;
    line-height: 16px;
    color: var(--common_level2_base_color);
  }

  &-more {
    display: flex;
    margin-left: 2px;
    align-items: center;
    display: inline-block;

    .arrow-icon {
      font-size: 18px;
      color: #999;
    }
  }

  &-divider {
    height: 0.5px;
    background-color: var(--common_line_light_color);
    margin: 8px 0 2px 0;
  }

  &-content {
    display: flex;
    flex-direction: column;
  }
}

.dtm-page-container-body {
  .customer-reviews {
    margin: 0 !important;
  }
}

.comments-page {
  .customer-reviews {
    margin: 16px 16px 0 16px !important;
  }
}
