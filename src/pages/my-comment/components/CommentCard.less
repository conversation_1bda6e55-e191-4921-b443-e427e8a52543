@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.my-comment-card {
  display: flex;
  background: @common_bg_z1_color;
  border-radius: @common_border_radius_l;
  margin-bottom: 8px;
  padding: 8px;
  .common_box_shadow_s();
}

.my-comment-card-img {
  width: 100px;
  height: 100px;
  border-radius: @common_border_radius_m;
  object-fit: cover;
  margin-right: 16px;
}

.my-comment-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  min-width: 0; // 确保 flex 项能够收缩
}

.my-comment-card-title {
  .common_action_text_style_mob();
  color: @common_level2_base_color;
  margin-bottom: 4px;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100%;
  word-break: break-all;
}

.my-comment-card-date {
  .common_action_text_style_mob();
  color: @common_level2_base_color;
  margin-bottom: 8px;
}

.my-comment-card-comment {
  .common_body_text_style_mob();
  color: @common_level1_base_color;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
