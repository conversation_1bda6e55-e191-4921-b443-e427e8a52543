import React from 'react';
import { RecUserCommentModel } from '@/apis/feed-action';
import { parseCommentContent } from '@/utils/commentUtils';
import './CommentCard.less';

interface CommentCardProps {
  data: RecUserCommentModel;
  onCommentClick?: (comment: RecUserCommentModel) => void;
}

const CommentCard: React.FC<CommentCardProps> = ({ data, onCommentClick }) => {
  const handleClick = () => {
    onCommentClick?.(data);
  };

  // 格式化时间戳为相对时间
  const formatTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    if (days < 30) return `${Math.floor(days / 7)}周前`;
    if (days < 365) return `${Math.floor(days / 30)}个月前`;
    return `${Math.floor(days / 365)}年前`;
  };

  return (
    <div className="my-comment-card" onClick={handleClick}>
      <img className="my-comment-card-img" src={data.imageUrl} alt="" />
      <div className="my-comment-card-content">
        <div className="my-comment-card-title">{data.productTitle}</div>
        <div className="my-comment-card-date">{formatTime(data.commentTimestamp)}</div>
        <div className="my-comment-card-comment">{parseCommentContent(data.comment.content)}</div>
      </div>
    </div>
  );
};

export default CommentCard;
