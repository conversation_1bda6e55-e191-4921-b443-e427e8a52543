import React, { useEffect } from 'react';
import { RecUserCommentModel } from '@/apis/feed-action';
import { useMyCommentList, generateProductUrl } from './services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import CommentCard from './components/CommentCard';
import { useTitle } from '@/hooks/useTitle';
import './index.less';

const MyCommentPage: React.FC = () => {
  const {
    data: response,
    loading,
    error,
    run: loadComments,
  } = useMyCommentList();

  const comments = response?.data || [];
  const hasMore = response?.hasMore || false;
  const cursor = response?.cursor || undefined;

  useTitle('我的评论');

  // 初始加载
  useEffect(() => {
    loadComments({ size: 20 });
  }, []);

  // 加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading && cursor !== undefined) {
      loadComments({ cursor, size: 20 });
    }
  };

  // 点击跳转
  const handleCommentClick = (comment: RecUserCommentModel) => {
    const url = generateProductUrl(comment);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={comments}
      renderItem={(item) => (
        <CommentCard key={item.commentId} data={item} onCommentClick={handleCommentClick} />
      )}
      loading={loading}
      error={!!error}
      hasMore={hasMore}
      layout="list"
      onLoadMore={handleLoadMore}
      emptyText={'没有评论记录'}
      errorText={'网络错误，请稍后重试'}
      noMoreText={'没有更多了'}
    />
  );
};

export default MyCommentPage;
