import { useRequest } from 'ahooks';
import {
  selectCommentByUid,
  RecFeedUserCommentListRequestModel,
  RecFeedUserCommentListResultModel,
  RecUserCommentModel,
} from '@/apis/feed-action';

// 获取我的评论列表
export const getMyCommentList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecUserCommentModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: RecFeedUserCommentListRequestModel = {
    cursor: params.cursor,
    size: params.size || 20,
  };

  const response: RecFeedUserCommentListResultModel = await selectCommentByUid(requestParams);

  return {
    data: response.commentInfoList,
    cursor: response.cursor,
    hasMore: response.hasMore,
  };
};

// 使用 ahooks useRequest 封装请求
export const useMyCommentList = () => {
  return useRequest(
    async (params: { cursor?: string; size?: number } = {}) => {
      return getMyCommentList(params);
    },
    {
      defaultParams: [{ size: 20 }],
      refreshDeps: [],
      cacheKey: 'my-comment-list',
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
      refreshOnWindowFocus: true, // 窗口聚焦时刷新
      retryCount: 3, // 重试次数
      onError: (error) => {
        console.error('获取评论列表失败:', error);
      },
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (comment: RecUserCommentModel): string => {
  // 使用商品跳转链接
  return comment.jumpUrl || '#';
};
