import React from 'react';
import { InfiniteList } from '@/components/InfiniteList';
import { useProductFavorites } from '../services';
import './ProductView.less';

const ProductView: React.FC = () => {
  const {
    data: response,
    loading,
    error,
  } = useProductFavorites();

  const products = response?.data || [];

  // 渲染单个商品卡片
  const renderProductItem = (product: any) => (
    <div className="favorites-product-card" key={product.productId}>
      <img className="favorites-product-img" src={product.primaryImage} alt={product.title} />
      <div className="favorites-product-info">
        <div className="favorites-product-title">{product.title}</div>
        <div className="favorites-product-price">¥{product.price}</div>
      </div>
      <div className="favorites-product-like">
        <span className="favorites-icon-heart">❤</span>
      </div>
    </div>
  );

  return (
    <InfiniteList
      data={products}
      renderItem={renderProductItem}
      loading={loading}
      error={!!error}
      hasMore={response?.hasMore || false}
      layout="list"
      emptyText="暂无收藏的商品"
      errorText="网络错误，请稍后重试"
      className="favorites-product-list"
    />
  );
};

export default ProductView;
