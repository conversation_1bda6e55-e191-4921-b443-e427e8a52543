@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.store-list {
  padding: 8px;
}

.store-card {
  display: flex;
  align-items: center;
  background: @common_bg_z1_color;
  border-radius: @common_border_radius_l;
  margin-bottom: 8px;
  padding: 8px;
  .common_box_shadow_s();
}

.store-logo {
  width: 100px;
  height: 100px;
  border-radius: @common_border_radius_m;
  object-fit: contain;
  background: @common_bg_color;
  border: 1px dashed @common_level4_base_color;
  margin-right: 16px;
}

.store-info {
  flex: 1;
  display: flex;
  min-height: 100px;
  flex-direction: column;
  justify-content: center;
}

.store-name {
  .common_body_bold_text_style();
  color: @common_level1_base_color;
  margin: 8px 0;

}

.store-meta {
  .common_description_text_style_mob();
  color: @common_level2_base_color;
  display: flex;
  gap: 16px;
}

.store-like {
  font-size: 24px;
  color: @theme_primary1_color;
  margin-left: 8px;
}
