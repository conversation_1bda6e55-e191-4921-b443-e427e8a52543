import React from 'react';
import { InfiniteList } from '@/components/InfiniteList';
import { useStoreFavorites } from '../services';
import './StoreView.less';

const StoreView: React.FC = () => {
  const {
    data: response,
    loading,
    error,
  } = useStoreFavorites();

  const stores = response?.data || [];

  // 渲染单个店铺卡片
  const renderStoreItem = (store: any) => (
    <div className="store-card" key={store.creatorId}>
      <img className="store-logo" src={store.avatar} alt={store.name} />
      <div className="store-info">
        <div className="store-name">{store.name}</div>
        <div className="store-meta">
          <span>{store.feedCount}</span>
          <span>{store.followerCount}</span>
        </div>
        <div className="store-meta">
          <span>投稿</span>
          <span>フォロワー</span>
        </div>
      </div>
      <div className="store-like">
        <span className="icon-heart">❤</span>
      </div>
    </div>
  );

  return (
    <InfiniteList
      data={stores}
      renderItem={renderStoreItem}
      loading={loading}
      error={!!error}
      hasMore={response?.hasMore || false}
      layout="list"
      emptyText="暂无收藏的店铺"
      errorText="网络错误，请稍后重试"
      className="store-list"
    />
  );
};

export default StoreView;
