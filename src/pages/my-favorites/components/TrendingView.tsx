import React, { useEffect } from 'react';
import { RecFeedDetail, queryUserCollectedFeedList } from '@/apis/feed-action';
import { useRequest } from 'ahooks';
import { TENANT_ID } from '@/common/constants';
import { generateProductUrl } from '../services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import ProductCard from '../../my-like/components/ProductCard';

const PAGE_SIZE = 20;

const getCollectedFeedList = async (params: { cursor?: string; size?: number }) => {
  const res = await queryUserCollectedFeedList({
    tenantId: TENANT_ID,
    cursor: params.cursor || undefined,
    size: params.size || PAGE_SIZE,
  });
  return {
    data: res.feeds,
    cursor: res.cursor,
    hasMore: res.hasMore,
  };
};

const TrendingView: React.FC = () => {
  const {
    data: response,
    loading,
    error,
    run: loadProducts,
  } = useRequest(
    async (params: { cursor?: string; size?: number } = {}) => {
      return getCollectedFeedList(params);
    },
    {
      defaultParams: [{ cursor: undefined, size: PAGE_SIZE }],
      refreshDeps: [],
      cacheKey: 'collected-feed-list',
      staleTime: 5 * 60 * 1000,
    },
  );

  const products = response?.data || [];
  const hasMore = response?.hasMore || false;
  const cursor = response?.cursor || undefined;

  // 初始加载
  useEffect(() => {
    loadProducts({ size: PAGE_SIZE });
  }, []);

  // 加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading && cursor !== undefined) {
      loadProducts({ cursor, size: PAGE_SIZE });
    }
  };

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item) => (
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      )}
      loading={loading}
      error={!!error}
      hasMore={hasMore}
      layout="waterfall"
      onLoadMore={handleLoadMore}
      emptyText={'没有收藏的产品'}
      errorText={'网络错误，请稍后重试'}
      noMoreText={'没有更多了'}
    />
  );
};

export default TrendingView;
