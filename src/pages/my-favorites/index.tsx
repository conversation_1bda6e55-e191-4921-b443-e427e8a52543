import React, { useState } from 'react';
import TrendingView from './components/TrendingView';
import StoreView from './components/StoreView';
import ProductView from './components/ProductView';
import { Tabs } from 'dingtalk-design-mobile';
import { useTitle } from '@/hooks/useTitle';
import './index.less';


function MyFavorites() {
  useTitle('我的收藏');

  const tabs = [
    { title: 'Trending', key: 'trending' },
    { title: 'Store', key: 'store' },
    { title: 'Product', key: 'product' },
  ];

  return (
    <div className="my-favorites-page">
      <Tabs
        className="my-favorites-tabs"
        tabs={tabs}
        onChange={(index) => {
          console.log('onChange', index);
        }}
        onTabClick={(index) => {
          console.log('onTabClick', index);
        }}
        hideBottomDivider
      >
        <div key="1">
          <TrendingView />
        </div>
        <div key="2">
          <StoreView />
        </div>
        <div key="3">
          <ProductView />
        </div>
      </Tabs>
    </div>
  );
}

export default MyFavorites;
