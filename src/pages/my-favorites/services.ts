
import { useRequest } from 'ahooks';
import {
  queryUserFavoriteFeedStoreList,
  IRecFavoriteFeedStoreListRequest,
  IRecFavoriteFeedStoreListResponse,
  IRecFeedStore,
  queryUserFavoriteProductList,
  IJUserProductRequest,
  IJUserProductResponse,
  IProjectModel,
} from '@/apis/user-recommend';
import { RecFeedDetail } from '@/apis/feed-action';
import { TENANT_ID } from '@/common/constants';

// 获取收藏的 Store 列表
export const getStoreFavorites = async (): Promise<{
  data: IRecFeedStore[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: IRecFavoriteFeedStoreListRequest = {
    tenantId: TENANT_ID,
  };

  const response: IRecFavoriteFeedStoreListResponse = await queryUserFavoriteFeedStoreList(requestParams);

  return {
    data: response.stores,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 ahooks useRequest 封装请求
export const useStoreFavorites = () => {
  return useRequest(
    async () => {
      return getStoreFavorites();
    },
    {
      defaultParams: [],
      refreshDeps: [],
      cacheKey: 'store-favorites-list',
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
      refreshOnWindowFocus: true, // 窗口聚焦时刷新
    },
  );
};

// 获取收藏的商品列表
export const getProductFavorites = async (): Promise<{
  data: IProjectModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: IJUserProductRequest = {
    cursor: undefined,
    size: 20,
  };

  const response: IJUserProductResponse = await queryUserFavoriteProductList(requestParams);

  return {
    data: response.products,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 ahooks useRequest 封装商品收藏请求
export const useProductFavorites = () => {
  return useRequest(
    async () => {
      return getProductFavorites();
    },
    {
      defaultParams: [],
      refreshDeps: [],
      cacheKey: 'product-favorites-list',
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
      refreshOnWindowFocus: true, // 窗口聚焦时刷新
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (product: RecFeedDetail): string => {
  // 使用 dingtalk 协议拼接 URL，itemId 作为 feedId
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${product.itemId}`;
};
