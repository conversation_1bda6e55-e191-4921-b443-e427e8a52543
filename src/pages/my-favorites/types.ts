// Trending 复用 my-like 的 ILikeProduct 类型
export interface ILikeProduct {
  id: string;
  title: string;
  titleTranslated: string;
  primaryImage: string;
  imageWidth?: number;
  imageHeight?: number;
  price: string;
  priceUnit: string;
  monthlySales: string;
  platform: string;
  platformLogo?: string;
  detailUrl?: string;
  productId: string;
}

// Store 收藏类型 - 使用 IRecFeedStore 从 @/apis/user-recommend

// Product 收藏类型
export interface IProductFavorite {
  id: string;
  title: string;
  image: string;
  price: string;
  priceUnit: string;
  isLiked: boolean;
}
