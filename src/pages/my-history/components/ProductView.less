@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.favorites-product-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 0;
}

.favorites-product-card {
  display: flex;
  align-items: center;
  background: @common_bg_z1_color;
  border-radius: @common_border_radius_l;
  .common_box_shadow_s();
  padding: 16px;
  margin: 0 8px;
  transition: box-shadow @common_light_motion_duration @common_light_motion_timing_function;
}

.favorites-product-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: @common_border_radius_m;
  margin-right: 16px;
  background: @common_bg_color;
}

.favorites-product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100px;
}

.favorites-product-title {
  .common_body_text_style_mob();
  color: @common_level1_base_color;
  margin: 8px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.favorites-product-price {
  .common_supertitle_text_style_mob();
  color: @theme_danger1_color;
  font-weight: bold;
  line-height: 1;
}

.favorites-product-like {
  display: flex;
  align-items: flex-end;
  margin-left: 12px;
}

.favorites-icon-heart {
  color: @theme_danger1_color;
  font-size: 24px;
  user-select: none;
}
