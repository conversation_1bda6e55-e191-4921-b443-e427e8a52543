import React, { useEffect } from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { useBrowseProductList, generateProductUrl } from '../services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import ProductCard from '../../my-like/components/ProductCard';

const TrendingView: React.FC = () => {
  const {
    data: response,
    loading,
    error,
    run: loadProducts,
  } = useBrowseProductList();

  const products = response?.data || [];
  const hasMore = response?.hasMore || false;
  const cursor = response?.cursor || undefined;

  // 初始加载
  useEffect(() => {
    loadProducts({ cursor: undefined, size: 20 });
  }, []);

  // 加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading && cursor !== undefined) {
      loadProducts({ cursor, size: 20 });
    }
  };

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item: RecFeedDetail) => (
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      )}
      loading={loading}
      error={!!error}
      hasMore={hasMore}
      layout="waterfall"
      onLoadMore={handleLoadMore}
    />
  );
};

export default TrendingView;
