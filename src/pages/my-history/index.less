@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.my-favorites-tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: @common_bg_z0_color;
  padding: 8px 0 8px 0;
  border-bottom: 1px solid @common_level4_base_color;
}

.my-favorites-tab-item {
  flex: 1;
  text-align: center;
  .common_action_text_style_mob();
  color: @common_level2_base_color;
  padding-bottom: 8px;
  cursor: pointer;
  position: relative;
  transition: color @common_light_motion_duration @common_light_motion_timing_function;
}

.my-favorites-tab-item.active {
  color: @theme_primary1_color;
  font-weight: 600;
}

.my-favorites-tab-item.active::after {
  content: '';
  display: block;
  margin: 0 auto;
  width: 32px;
  height: 3px;
  border-radius: @common_border_radius_s;
  background: @theme_primary1_color;
  position: absolute;
  left: 50%;
  bottom: -8px;
  transform: translateX(-50%);
}

.my-favorites-tab-content {
  background: @common_bg_color;
  min-height: 80vh;
  padding-top: 8px;
}
