import React, { useState } from 'react';
import TrendingView from './components/TrendingView';
import ProductView from './components/ProductView';
import { Tabs } from 'dingtalk-design-mobile';
import { useTitle } from '@/hooks/useTitle';
import './index.less';


function MyHistory() {
  useTitle('浏览历史');

  const tabs = [
    { title: 'Trending', key: '1' },
    { title: 'Product', key: '2' },
  ];

  return (
    <div className="my-favorites-page">
      <Tabs
        className="my-favorites-tabs"
        tabs={tabs}
        onChange={(index) => {
          console.log('onChange', index);
        }}
        onTabClick={(index) => {
          console.log('onTabClick', index);
        }}
        hideBottomDivider
      >
        <div key="1">
          <TrendingView />
        </div>
        <div key="2">
          <ProductView />
        </div>
      </Tabs>
    </div>
  );
}

export default MyHistory;
