
import { useRequest } from 'ahooks';
import {
  queryBrowseHistory,
  RecQueryBrowseHistoryRequest,
  RecQueryBrowseHistoryResponse,
  RecFeedDetail,
} from '@/apis/feed-action';
import {
  queryUserFootprint,
  IJUserProductRequest,
  IJUserProductResponse,
  IProjectModel,
} from '@/apis/user-recommend';
import { TENANT_ID } from '@/common/constants';

// 获取浏览历史商品列表
export const getBrowseProductList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecFeedDetail[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: RecQueryBrowseHistoryRequest = {
    tenantId: TENANT_ID, // 使用常量
    cursor: params.cursor || undefined,
    size: params.size || 20,
  };

  const response: RecQueryBrowseHistoryResponse = await queryBrowseHistory(requestParams);

  return {
    data: response.items,
    cursor: response.cursor,
    hasMore: response.cursor !== '', // 如果cursor为空字符串，说明没有更多数据
  };
};

// 使用 ahooks useRequest 封装请求
export const useBrowseProductList = () => {
  return useRequest(
    async (params: { cursor?: string; size?: number } = {}) => {
      return getBrowseProductList(params);
    },
    {
      defaultParams: [{ cursor: undefined, size: 20 }],
      refreshDeps: [],
      cacheKey: 'browse-product-list',
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
      refreshOnWindowFocus: true, // 窗口聚焦时刷新
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (product: RecFeedDetail): string => {
  // 使用 dingtalk 协议拼接 URL，itemId 作为 feedId
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${product.itemId}`;
};

// 获取历史浏览商品列表
export const getHistoryProductList = async (): Promise<{
  data: IProjectModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: IJUserProductRequest = {
    cursor: undefined,
    size: 20,
  };

  const response: IJUserProductResponse = await queryUserFootprint(requestParams);

  return {
    data: response.products,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 ahooks useRequest 封装历史浏览商品请求
export const useHistoryProductList = () => {
  return useRequest(
    async () => {
      return getHistoryProductList();
    },
    {
      defaultParams: [],
      refreshDeps: [],
      cacheKey: 'history-product-list',
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      cacheTime: 10 * 60 * 1000, // 10分钟缓存时间
      refreshOnWindowFocus: true, // 窗口聚焦时刷新
    },
  );
};
