// Trending 复用 my-like 的 ILikeProduct 类型
export interface ILikeProduct {
  id: string;
  title: string;
  titleTranslated: string;
  primaryImage: string;
  imageWidth?: number;
  imageHeight?: number;
  price: string;
  priceUnit: string;
  monthlySales: string;
  platform: string;
  platformLogo?: string;
  detailUrl?: string;
  productId: string;
}

// Store 收藏类型
export interface IStoreFavorite {
  id: string;
  name: string;
  logo: string;
  postCount: number;
  followerCount: number;
  isLiked: boolean;
}

// Product 收藏类型
export interface IProductFavorite {
  id: string;
  title: string;
  image: string;
  price: string;
  priceUnit: string;
  isLiked: boolean;
}
