import React, { FC, useEffect, useRef, useState } from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { IFeedPayload } from '@/common/types';
import './ProductCard.less';

// import { i18next } from '@ali/dingtalk-i18n';

interface ProductCardProps {
  product: RecFeedDetail;
  onProductClick: (product: RecFeedDetail) => void;
}

const ProductCard: FC<ProductCardProps> = ({ product, onProductClick }) => {
  const imageRef = useRef<HTMLDivElement>(null);
  const [imageHeight, setImageHeight] = useState('100%'); // 默认 1:1

  // 解析 payload 字段
  const parsePayload = () => {
    try {
      return JSON.parse(product.payload || '{}');
    } catch (e) {
      console.warn('解析 product payload 失败:', e);
      return {};
    }
  };

  const payload: IFeedPayload = parsePayload();
  const goods = payload?.goods;

  // 获取商品信息
  const productTitle = goods?.name || product.itemName || '未知商品';
  const productIntro = goods?.intro || product.itemName || '未知商品';
  const productImage = goods?.image || product.coverImg || '';
  const productMargin = goods?.grossMargin ? `毛利率${goods.grossMargin}` : '';
  const productPlatform = goods?.bizType || '未知平台';

  useEffect(() => {
    // 优先使用数据中的图片宽高信息
    if (productImage) {
      // 如果没有宽高信息，则动态获取图片尺寸
      const tempImg = new Image();
      tempImg.onload = () => {
        const { naturalWidth, naturalHeight } = tempImg;
        const aspectRatio = naturalHeight / naturalWidth;

        // 限制高度比例在 1:1 到 3:2 之间
        const minRatio = 1; // 最小 1:1
        const maxRatio = 1.5; // 最大 3:2 (9/6)

        const clampedRatio = Math.max(minRatio, Math.min(maxRatio, aspectRatio));
        setImageHeight(`${clampedRatio * 100}%`);
      };
      tempImg.src = productImage;
    }
  }, [productImage]);

  const handleClick = () => {
    onProductClick(product);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'zozotown':
        return 'Z';
      case 'amazon':
        return 'A';
      case 'rakuten':
        return 'R';
      case 'fashion':
        return 'F';
      default:
        return platform.charAt(0);
    }
  };

  const getPlatformClass = (platform: string) => {
    return platform.toLowerCase().replace(/\s+/g, '');
  };

  return (
    <div className="product-card" onClick={handleClick}>
      <div
        className="product-image-container"
        ref={imageRef}
        style={{ paddingBottom: imageHeight }}
      >
        <img
          src={productImage}
          alt={productTitle}
          className="product-image"
          loading="lazy"
        />
      </div>
      <div className="product-info">
        <div className="product-title">
          {productIntro || productTitle}
        </div>
        <div className="product-sales">
          {productMargin}
        </div>
        <div className="product-platform">
          <div className={`platform-icon ${getPlatformClass(productPlatform)}`}>
            {getPlatformIcon(productPlatform)}
          </div>
          <span className="platform-name">{productPlatform}</span>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
