import React, { useEffect } from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { useLikeProductList, generateProductUrl } from './services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import ProductCard from './components/ProductCard';
import { useTitle } from '@/hooks/useTitle';
import './index.less';

const MyLikePage: React.FC = () => {
  const {
    data: response,
    loading,
    error,
    run: loadProducts,
  } = useLikeProductList();

  const products = response?.data || [];
  const hasMore = response?.hasMore || false;
  const cursor = response?.cursor || undefined;

  useTitle('我赞过的');

  // 初始加载
  useEffect(() => {
    loadProducts({ size: 20 });
  }, []);

  // 加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading && cursor !== undefined) {
      loadProducts({ cursor, size: 20 });
    }
  };

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item) => (
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      )}
      loading={loading}
      error={!!error}
      hasMore={hasMore}
      layout="waterfall"
      onLoadMore={handleLoadMore}
      emptyText={'没有点赞的产品'}
      errorText={'网络错误，请稍后重试'}
      noMoreText={'没有更多了'}
    />
  );
};

export default MyLikePage;
