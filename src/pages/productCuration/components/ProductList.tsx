import { i18next } from '@ali/dingtalk-i18n';
import {
  Typography,
} from 'dingtalk-design-desktop';
import jsapi_openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { IProduct } from '@/common/types';

const { Text } = Typography;

const ProductList = ({
  products,
  toggleProductSelection,
}: {products: IProduct[];toggleProductSelection: (productId: string) => void}) => {
  return (
    <div className="product-grid">
      {products?.map((product) =>
        (
          <div
            className={`product-card ${product.selected ? 'selected' : ''}`}
            key={product.productId}
            onClick={() => toggleProductSelection(product.productId)}
          >
            {product.selected &&
            <div className="product-checkbox">
              <img src="https://img.alicdn.com/imgextra/i4/O1CN01x0ws381pB4WXFeZjg_!!6000000005321-55-tps-200-200.svg" alt="tag" />
            </div>
            }

            <div className="product-image-container">
              <img
                src={product.primaryImage}
                alt={product.title}
                className="product-image"
              />
              <div
                className="product-view-detail"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  const url = `dingtalk://dingtalkclient/action/open_platform_link?pcLink=${encodeURIComponent(`dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(product.detailHtml)}&popup_wnd=true&height=800&width=1200`)}&mobileLink=${encodeURIComponent(`https://applink.dingtalk.com/page/link?url=${encodeURIComponent(product.detailHtml)}&target=panel`)}`;
                  jsapi_openLink({ url });
                }}
              >
                {i18next.t('j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails')}
              </div>
            </div>

            <div className="product-info">
              <h3 className="product-name">{product.title}</h3>
              <div className="product-bottom">
                <div className="product-price-row">
                  <Text className="product-price">{product?.salePrice}円</Text>
                </div>
                {
                  product?.score && (
                    <div className="product-rating">
                      <span className="product-score">{i18next.t('j-agent-web_pages_productCuration_components_ProductList_Rating')}</span>
                      <span className="rating-value">{product?.score}</span>
                    </div>
                  )
                }
              </div>
            </div>
          </div>
        ))}
    </div>);
};

export default ProductList;
