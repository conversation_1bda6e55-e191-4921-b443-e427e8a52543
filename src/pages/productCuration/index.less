html,body{
  margin: 0;
  padding: 0;
}

:global {
  #dingapp {
    width: 100vw;
    height: 100vh;
    background-color: var(--common_fg_color);
  }
	ul {
		padding: 0;
		margin: 0;
		list-style: none;
	}
	li {
		margin-left: 0;
	}
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 16px 0;
  padding-bottom: env(safe-area-inset-bottom);

  .product-empty-list {
    height: 80vh;
  }
}

// .header {
//   margin-bottom: 16px;
//   text-align: center;
//   padding-bottom: 16px;
//   border-bottom: 1px solid var(--common_line_hard_color);
//   position: relative;
// }

// .header h1 {
//   font-size: 28px;
//   color: var(--common_level1_base_color);
//   margin-bottom: 8px;
//   font-weight: 600;
//   letter-spacing: 0.5px;
// }

.page-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: var(--common_level3_base_color);
  font-size: 14px;
}

.product-list-container {
  background-color: var(--common_fg_color);
  border-radius: 8px;
  box-shadow: var(--common_box_shadow_s);
  padding-bottom: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.selection-button {
  padding: 0 8px;
  cursor: pointer;
  &:hover {
    color: var(--theme_primary_hover_color);
  }
  &:active {
    color: var(--theme_primary_press_color);
  }
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.product-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  background-color: var(--common_fg_z10_color);
  transition: all 0.3s ease;
  border: 1px solid var(--common_line_light_color);
  position: relative;
  overflow: hidden;
  height: 100%;
  &.selected {
    border-color: #FF0E53;
  }
  &:hover {
    cursor: pointer;
  }
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--common_box_shadow_m);
  cursor: pointer;
}

.product-checkbox {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 0;
  z-index: 2;
  img {
    width: 26px;
    height: 26px;
  }
}

.product-image-container {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  padding-top: 100%;
  .product-view-detail {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 28px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
      cursor: pointer;
    }
  }
}

.product-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

.product-info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--common_level1_base_color);
  line-height: 1.4;
  /* 文本超出两行显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px;
}

.product-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-rating {
  display: flex;
  align-items: center;
}

.product-score {
  font-size: 16px;
  color: var(--common_level3_base_color);
}

.rating-value {
  font-size: 16px;
  font-weight: bold;
  color: #FF0E53;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: #FF0E53;
  display: flex;
  align-items: center;
}

.product-discount {
  margin-left: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin: 24px 0 16px;
}

.submit-button {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  position: sticky;
  bottom: 16px;
  z-index: 10;
}

.submit-button button {
  font-size: 16px;
  height: 44px;
  max-width: 80%;
  padding: 0 24px;
  border-radius: 22px;
  box-shadow: var(--common_box_shadow_s);
  background-color: #FF0E53 !important;
  color: #fff !important;
  transition: all 0.3s ease;
  cursor: pointer;
  &[disabled],
  &[disabled]:hover,
  &[disabled]:active,
  &[disabled]:focus {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
  }
}

.submit-button button:hover {
  transform: translateY(-2px);
  box-shadow: var(--common_box_shadow_m);
}

/* 钉钉组件样式覆盖 */
.dingtalk-btn {
  border-radius: 6px;
  transition: all 0.3s;
  font-weight: 500;
}

.dingtalk-pagination-item {
  border-radius: 6px;
}

.dingtalk-list-item {
  padding: 0;
  margin-bottom: 16px;
}

.dingtalk-rate-star {
  margin-right: 2px;
  font-size: 14px !important;
}

.dingtalk-tag {
  border-radius: 4px;
  padding: 0 6px;
  font-weight: 500;
  font-size: 12px;
}

/* 添加其他辅助样式 */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--common_line_light_color);
}

/* 页面过渡动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .product-score {
    font-size: 12px;
  }
  
  .rating-value {
    font-size: 12px;
  }
  
  .page-info {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .pagination {
    margin: 16px 0;
  }

  .submit-button {
    margin-bottom: 24px;
  }

  .submit-button button {
    width: 100%;
    font-size: 14px;
    height: 40px;
  }
}
