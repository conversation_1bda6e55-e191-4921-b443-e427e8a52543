import { i18next } from '@ali/dingtalk-i18n'; import { useState, useEffect } from 'react';
import {
  Button,
  Pagination,
  ConfigProvider,
  message,
  Spin,
  Empty,
} from 'dingtalk-design-desktop';
import { getProductListCache, sendProductToFeeds } from '@/apis/curation';
import ProductList from './components/ProductList';
import { getDecodedUrlParam } from '@/utils/util';
import { setPageTitle } from '@/utils/jsapi';
import { log } from '@/utils/console';
import { IProduct } from '@/common/types';
import './index.less';

const App = () => {
  const [products, setProducts] = useState<IProduct[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const pageSize = 12;

  useEffect(() => {
    setPageTitle(i18next.t('j-agent-web_pages_productCuration_ProductSelectionCenter'));
  }, []);

  // 加载数据
  useEffect(() => {
    setLoading(true);
    const bizId = getDecodedUrlParam('bizId');
    if (!bizId) {
      message.error(i18next.t('j-agent-web_pages_productCuration_UnableToObtainBizid'));
      return;
    }
    getProductListCache({
      bizId,
    }).then((res) => {
      log('getProductListCache: ', res);
      if (res?.success) {
        // 默认不选中
        const selectedProducts = res?.productDetails?.map((product: any) => ({
          ...product,
          selected: false,
        }));
        setProducts(selectedProducts || []);
      } else {
        message.error(res?.errorMsg || i18next.t('j-agent-web_pages_productCuration_FailedToObtainProductList'));
      }
      setLoading(false);
    }).catch((err) => {
      log.error(i18next.t('j-agent-web_pages_productCuration_FailedToObtainProductList'), err);
      message.error(i18next.t('j-agent-web_pages_productCuration_FailedToObtainProductList'));
      setLoading(false);
    });
  }, []);

  // 切换选中状态
  const toggleProductSelection = (productId: string) => {
    setProducts((prevProducts) =>
      prevProducts.map((product) =>
        (product.productId === productId ?
          { ...product, selected: !product.selected } :
          product)));
  };

  // 切换当前页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到页面顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // 获取当前页的商品
  const getCurrentPageProducts = (): IProduct[] => {
    const startIndex = (currentPage - 1) * pageSize;
    return products.slice(startIndex, startIndex + pageSize);
  };

  // 全选/取消全选当前页商品
  const toggleSelectAllCurrentPage = () => {
    const currentPageProducts = getCurrentPageProducts();
    const allSelected = currentPageProducts.every((product) => product.selected);

    setProducts((prevProducts) =>
      prevProducts.map((product) => {
      // 只修改当前页的商品选中状态
        if (currentPageProducts.some((p) => p.productId === product.productId)) {
          return { ...product, selected: !allSelected };
        }
        return product;
      }));
  };

  // 提交选中的商品到feeds流
  const handleSubmit = () => {
    const selectedProducts = products.filter((product) => product.selected);
    const formattedData = selectedProducts.map((product) => ({
      sourceItemId: product.productId,
      sourceType: product.platform,
    }));
    log('formattedData: ', formattedData);
    sendProductToFeeds({
      request: formattedData,
    }).then((res) => {
      log('sendProductToFeeds: ', res);
      const { successItemList, failItemList } = res;
      message.success({
        content: i18next.t('j-agent-web_pages_productCuration_SuccessfullySubmitted', { successItemListLen: successItemList?.length || 0, failItemListLen: failItemList?.length || 0 }),
        duration: 2,
      });

      // 清空选择结果
      setProducts((prevProducts) => prevProducts.map((product) => ({
        ...product,
        selected: false,
      })));
    }).catch((err) => {
      log.error(i18next.t('j-agent-web_pages_productCuration_FailedToSubmitTheProduct'), err);
      message.error(i18next.t('j-agent-web_pages_productCuration_FailedToSubmitTheProduct'));
    });
  };

  const selectedCount = products.filter((p) => p.selected).length || 0;
  const currentPageProducts = getCurrentPageProducts();
  const isCurrentPageAllSelected = currentPageProducts.length > 0 &&
  currentPageProducts.every((product) => product.selected);

  return (
    <ConfigProvider>
      <div className="container">
        <Spin spinning={loading} tip={i18next.t('j-agent-web_pages_productCuration_LoadingGoods')}>
          {!loading ? (
            <div className="product-list-container">
              {products.length > 0 ? (
                <>
                  <div className="list-header">
                    <div className="page-info">{i18next.t('j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount', { productsLength: products.length, selectedCount })}

                    </div>
                    <div className="selection-controls">
                      <Button
                        type="link"
                        onClick={toggleSelectAllCurrentPage}
                        className="selection-button"
                      >
                        {isCurrentPageAllSelected ? i18next.t('j-agent-web_pages_productCuration_DeselectAll') : i18next.t('j-agent-web_pages_productCuration_SelectAllCurrentPage')}
                      </Button>
                    </div>
                  </div>

                  <ProductList
                    products={currentPageProducts}
                    toggleProductSelection={toggleProductSelection}
                  />


                  <div className="pagination">
                    <Pagination
                      hideOnSinglePage
                      current={currentPage}
                      pageSize={pageSize}
                      total={products.length}
                      onChange={handlePageChange}
                      showQuickJumper={false}
                      showSizeChanger={false}
                      showTotal={(total, range) => `${range[0]}-${range[1]} / ${total}`}
                    />
                  </div>
                </>
              ) : (
                <Empty description={i18next.t('j-agent-web_pages_productCuration_NoProductDataAvailable')} />
              )}
            </div>
          ) : (
            <div className="product-empty-list" />
          )}
        </Spin>
        {
          products?.length > 0 && (
            <div className="submit-button">
              <Button
                type="primary"
                size="large"
                onClick={handleSubmit}
                disabled={!products.some((p) => p.selected)}
                loading={loading}
              >
                {i18next.t('j-agent-web_pages_productCuration_SubmitSelectionSelectedcount', { selectedCount })}
              </Button>
            </div>
          )
        }
      </div>
    </ConfigProvider>);
};

export default App;
