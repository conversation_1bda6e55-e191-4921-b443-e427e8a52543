import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import './style.less';
import { IOriginProduct, IProductData } from '@/common/types';
import { Toast } from 'dingtalk-design-mobile';
import { getDecodedUrlParam, getUrlParam } from '@/utils/util';
import { setPageTitle } from '@/utils/jsapi';
import { queryImageSearch } from '@/apis';
import { createProductRankingsArray } from '@/utils/productRankings';
import Skeleton from '../Skeleton';

const ProductPage: React.FC = () => {
  const [productData, setProductData] = useState<IProductData>();
  const [list, setList] = useState<IOriginProduct[]>([]);
  const title = getDecodedUrlParam('title');
  const url = getDecodedUrlParam('url');
  const price = getUrlParam('price') || '';
  const listKey = getUrlParam('listKey') || 'salesProductList';
  const [loading, setLoading] = useState<boolean>(true);

  // Use utility function to generate product rankings
  const productRankings = createProductRankingsArray();

  // 初始化商品元素引用数组
  useEffect(() => {
    setPageTitle(i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing'));
    initData();
  }, []);

  // 初始化数据
  const initData = () => {
    const originProductId = getUrlParam('productId') || '';
    setLoading(true);
    queryImageSearch({
      originProductId,
      urls: [url],
      title,
    }).then((res) => {
      if (!res.success) {
        Toast.fail({
          content: res.message || i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        return;
      }
      setProductData(res.originProduct);
      setList(res.products);
      setLoading(false);
    }).catch((err) => {
      Toast.fail({
        content: err.message || i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    });
  };

  // 打开商品详情
  const openProductDetail = (item: IOriginProduct) => {
    $openLink({
      url: `https://applink.dingtalk.com/page/link?url=${encodeURIComponent(item.detailHtml)}&target=panel`,
      enableShare: true,
    });
  };

  if (loading) {
    return <Skeleton />;
  }

  return (
    <div className="productOrigin-page-container">
      <div className="productOrigin">
        <div className="img-container">
          <img
            alt="business-1"
            width="100"
            height="100"
            decoding="async"
            src={productData.image}
            loading="lazy"
          />
        </div>
        <div className="discription-container">
          <div className="product-title">
            {productData.title || title}
          </div>
          <div className="product-price">
            <div className="origin">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid')}</div>
            <div className="price">{productData.price || price}</div>
          </div>
        </div>
      </div>

      <div className="product-detail">
        <div className="product-detail-title">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_Details')}</div>
        <div className="detail-content">
          <div className="item">
            <div className="discription">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_Favorites')}</div>
            <div className="dis-content">{productData.favNum ? i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople', { productDataFavNum: productData.favNum }) : i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_NoCollection')}</div>
          </div>
          <div className="item">
            <div className="discription">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_List')}</div>
            <div className="dis-content">{productRankings.find((item) => item.id === listKey)?.rankingName}</div>
          </div>
        </div>
        <div className="product-detail-line">
          <div className="title">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductRating')}</div>
          <div className="content">{productData?.reviewAverageScore || i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_NoScore')}</div>
        </div>
      </div>
      <div>
        <div className="listTitle">{i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts')}</div>
        {
          list?.map((item) =>

            (<div
              className="productOrigin"
              style={{ marginTop: '16px', cursor: 'pointer' }}
              onClick={() => openProductDetail(item)}
            >


              <div className="img-container">
                <img
                  alt="business-1"
                  width="134"
                  height="134"
                  decoding="async"
                  src={item.primaryImage}
                  loading="lazy"
                />


              </div>
              <div className="discription-container">
                <div className="product-title">
                  {item.title}
                </div>
                <div className="product-price">
                  <div className="origin">1688</div>
                  <div className="price">{item?.priceInfo?.price}</div>
                </div>
              </div>
            </div>))
        }
      </div>
    </div>);
};

export default ProductPage;
