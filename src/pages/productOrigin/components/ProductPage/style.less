body{
  background-color: var(--common_fg_color);
}
.productOrigin-page-container {
  background-color: var(--common_fg_color);
  width: 100%;
  margin-left: auto;
  box-sizing: border-box;
  margin-right: auto;
  display: block;
  padding: 16px 16px 24px 16px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
  min-height: 100vh;

  .listTitle{
    color: var(--common_level1_base_color);
    font-family: Hiragino Sans;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-align: center;
  }
  // background-color: #fff;
  .productOrigin {
    display: flex;

    .img-container {
      margin-right: 8px;

      img {
        border-radius: 6px;
      }
    }

    .discription-container {
      flex: 1 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .product-title {
        width: 100%;
        color: var(--common_level1_base_color);
        text-overflow: ellipsis;
        font-family: Hiragino Sans;
        white-space: wrap;
        overflow: hidden;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 13px;
        font-style: normal;
        font-weight: 300;
        line-height: 18px;
      }

      .product-price {
        display: flex;
        flex-direction: column;

        .origin {
          color: var(--common_level3_base_color);
          font-family: Hiragino Sans;
          font-size: 10px;
          font-style: normal;
          font-weight: 300;
          line-height: 14px;
        }

        .price {
          --origin-font-size: 20px;
          --line-font-size: 10px;
          color: #F56646;
          --font-family: DIN Alternate;
          --line-font-weight: 300;
          --origin-font-weight: 600;
          --line-font-scale: 1;
          --origin-font-scale: 1;
        }
      }
    }
  }

  .product-detail {
    border-radius: 12px;
    margin-bottom: 8px;
    padding-top: 12px;
    .product-detail-title{
      color: var(--common_level1_base_color);
    }
    .detail-content {
      margin-top: 12px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      .item {
        flex: 1;
        border-right: 1px solid var(--common_line_light_color);
        &:last-child {
          border-right: none;
        }

        .discription {
          color: var(--common_level1_base_color);
          font-size: 12px;
          font-style: normal;
          font-weight: 300;
          line-height: normal;
          margin-bottom: 4px;
        }

        .dis-content {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          color: var(--common_level1_base_color);
        }
      }
    }
  }

  .product-detail-line {
    margin-top: 12px;

    .title {
      color: var(--common_level1_base_color);
      font-family: Hiragino Sans;
      font-size: 12px;
      font-style: normal;
      font-weight: 300;
      line-height: normal;
      margin-bottom: 4px;
    }

    .content {
      color: var(--common_level1_base_color);
      font-family: Hiragino Sans;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      display: flex;
      align-items: center;
    }
  }
}