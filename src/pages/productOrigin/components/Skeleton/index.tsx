import { Skeleton, WhiteSpace, WingBlank } from 'dingtalk-design-mobile';
import './index.less';

const SkeletonComponent: React.FC = () => {
  return (
    <div className="skeleton-container">
      <WingBlank>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <Skeleton type="text" animated />
        <WhiteSpace />
        <Skeleton type="text" width="60%" animated />
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
      </WingBlank>
    </div>
  );
};

export default SkeletonComponent;
