import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import { IOriginProduct, IProductData, IUTParams, EPlatform } from '@/common/types';
import { Toast } from 'dingtalk-design-mobile';
import { getDecodedUrlParam, getUrlParam, getPageConfig } from '@/utils/util';
import { isDingTalk, isPc, openLink, setPageTitle } from '@/utils/jsapi';
import { getShareUrl } from '@/utils/env';
import { sendUT } from '@/utils/trace';
import { queryImageSearch, queryProductByImage } from '@/apis';

import OptimizedImage from '@/components/OptimizedImage';
import SwipeableProductList from '@/components/SwipeableProductList';
import { decode } from 'he';
import Skeleton from '../Skeleton';
import './style.less';
import { InformationThereLOutlined } from '@ali/ding-icons';

// 设置分享按钮
const setShare = (productData: IProductData, title: string, utParams: IUTParams) => {
  const productId = getUrlParam('productId') || productData?.productId || getPageConfig('productId');

  if (!isDingTalk() || isPc || !productId) {
    return;
  }

  $setRight({
    show: true,
    control: true,
    text: '•••',
    onSuccess: () => {
      // 页面曝光
      sendUT('sourcing_page_share_success', utParams);
      $setShare({
        type: 0,
        url: getShareUrl(productId), // Use 7ding domain for sharing
        title: decode(productData?.title) || title || '',
        content: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ShareContent'),
        image: productData?.image,
      });
    },
  });
};

const rankTypeMap = {
  salesProductList: [
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume'),
    }],

  newProductList: [
    {
      type: 'favNum',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes'),
    }],

  soaringProductList: [
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume'),
    },
    {
      type: 'salesCountGrowthRate',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth'),
    }],

  opportunityProductList: [
    {
      type: 'daysCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe'),
    },
    {
      type: 'salesCount',
      name: i18next.t('j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume'),
    }],

};

const platform = getUrlParam('platform') as EPlatform;

const Page: React.FC = () => {
  const [productData, setProductData] = useState<IProductData>();
  const [list, setList] = useState<IOriginProduct[]>([]);
  const title = getDecodedUrlParam('productTitle');
  const url = getDecodedUrlParam('url');
  const price = getUrlParam('price') || '';
  const [loading, setLoading] = useState<boolean>(true);
  const rankType = getUrlParam('listKey');
  const bizId = getDecodedUrlParam('bizId');

  // State for tracking consecutive clicks
  const [clickCount, setClickCount] = useState<number>(0);
  const [lastClickTime, setLastClickTime] = useState<number>(0);

  // Handle consecutive clicks for copying productId
  const handleImageClick = () => {
    const currentTime = Date.now();
    const timeDiff = currentTime - lastClickTime;

    // Reset click count if more than 2 seconds have passed since last click
    if (timeDiff > 2000) {
      setClickCount(1);
    } else {
      setClickCount((prev) => prev + 1);
    }

    setLastClickTime(currentTime);

    // If clicked 6 times consecutively, copy productId to clipboard
    if (clickCount + 1 >= 6) {
      const productId = getUrlParam('productId') || getPageConfig('productId') || '';
      if (productId) {
        navigator.clipboard.writeText(productId);
        Toast.success({
          content: i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard'),
          position: 'top',
          maskClickable: true,
        });
      }
      // Reset click count after copying
      setClickCount(0);
    }
  };

  // 初始化商品元素引用数组
  useEffect(() => {
    if (isDingTalk()) {
      // 页面曝光
      sendUT('sourcing_page_view', {
        platform,
        reportType: getUrlParam('reportType'),
        bizId,
        rankType,
        productId: getUrlParam('productId'),
        productTitle: title,
      });
    }

    setPageTitle(i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing'));

    initData();
  }, []);

  // 初始化数据
  const initData = () => {
    setLoading(true);
    const productIdFromUrl = getUrlParam('productId');
    const productIdFromConfig = getPageConfig('productId');
    if (isDingTalk() && productIdFromUrl) {
      queryImageSearch({
        originProductId: productIdFromUrl,
        urls: [url],
        title,
        bizId,
      })
        .then((res) => {
          if (!res.success) {
            Toast.fail({
              content:
            res.message ||
            i18next.t(
              'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
            ),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
            return;
          }

          setProductData(res.originProduct);
          setList(res.products);
          setLoading(false);
          setShare(res.originProduct, title, {
            platform,
            reportType: getUrlParam('reportType'),
            bizId,
            rankType,
            productId: getUrlParam('productId'),
            productTitle: title,
          });
        })
        .catch((err) => {
          Toast.fail({
            content:
          err.message ||
          i18next.t(
            'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
          ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
        });
    } else if (productIdFromConfig) {
      queryProductByImage({
        originProductId: productIdFromConfig,
      })
        .then((res) => {
          const { data } = res;
          if (!data?.success) {
            Toast.fail({
              content:
            data?.errorMessage ||
            i18next.t(
              'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
            ),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
            return;
          }
          setProductData(data?.originProduct || {});
          setList(data?.products || []);
          setLoading(false);
          setShare(data.originProduct, title, {
            platform,
            reportType: getUrlParam('reportType'),
            bizId,
            rankType,
            productId: getUrlParam('productId'),
            productTitle: title,
          });
        })
        .catch((err) => {
          Toast.fail({
            content:
          err.message ||
          i18next.t(
            'j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData',
          ),
            duration: 3,
            position: 'top',
            maskClickable: true,
          });
        });
    }
  };

  // 打开商品详情
  const openProductDetail = (item: IOriginProduct) => {
    if (isDingTalk()) {
      // 商品点击
      sendUT('sourcing_product_detail_click', {
        platform,
        reportType: getUrlParam('reportType'),
        bizId,
        rankType,
        productId: item.productId,
        productTitle: item.titleTranslated || item.title,
      });

      openLink(item.detailHtml, true);
    } else {
      window.open(item.detailHtml, '_blank');
    }
  };

  // Handle product interested action
  const handleProductInterested = (product: IOriginProduct) => {
    // Send analytics
    sendUT('sourcing_product_interested', {
      platform,
      reportType: getUrlParam('reportType'),
      bizId,
      rankType,
      productId: product.productId,
      productTitle: product.titleTranslated || product.title,
    });

    Toast.success({
      content: i18next.t('j-dingtalk-web_pages_sourcing_components_Page_MarkedAsInterested'),
    });
  };

  // Handle product not interested action
  const handleProductNotInterested = (product: IOriginProduct) => {
    // Send analytics
    sendUT('sourcing_product_not_interested', {
      platform,
      reportType: getUrlParam('reportType'),
      bizId,
      rankType,
      productId: product.productId,
      productTitle: product.titleTranslated || product.title,
    });

    Toast.success({
      content: i18next.t('j-dingtalk-web_pages_sourcing_components_Page_MarkedAsNotInterested'),
      position: 'top',
      maskClickable: true,
    });
  };

  if (loading) {
    return <Skeleton />;
  }

  return (
    <div className="product-page-container">
      <div className="product-container">
        <div className="product-detail">
          <div className="img-container">
            <OptimizedImage
              src={productData.image || ''}
              alt="zozotown"
              width={120}
              height={120}
              lazy
              progressive
              quality={85}
              onClick={handleImageClick}
            />

            <div className="product-platform">{productData?.platform?.toUpperCase()}</div>
          </div>
          <div className="discription-container">
            <div className="product-title">{decode(productData?.title) || title || ''}</div>
            <div className="product-detail-bottom">
              <div className="price">
                <span style={{ marginLeft: '4px' }}>
                  {productData.price || price}
                  <span style={{ verticalAlign: 'baseline', fontSize: '12px' }}>
                    {productData.priceUnit || '円'}
                  </span>
                </span>
              </div>
              <div className="tag-wrap">
                <div className="product-stats">
                  {
                    rankType ?
                      rankTypeMap[rankType as keyof typeof rankTypeMap]?.map((item) => {
                        if (!productData[item.type]) {
                          return null;
                        }
                        return (
                          <span key={`${item.type}`} className="value">{`${item.name} ${
                            productData[item.type]}`
                          }
                          </span>);
                      }) :
                      null
                  }
                </div>
              </div>
              {productData?.storeName &&
              <div className="shopName">{decode(productData?.storeName || '')}</div>
              }
            </div>
          </div>
        </div>
        <div className="product-1688">
          <div className="product-1688-title">{i18next.t('j-dingtalk-web_pages_sourcing_components_Page_SimilarSources')}

            <InformationThereLOutlined className="product-1688-info" />
          </div>
        </div>
        <div className="product-list">
          <SwipeableProductList
            products={list}
            onProductInterested={handleProductInterested}
            onProductNotInterested={handleProductNotInterested}
            onProductClick={openProductDetail}
          />

        </div>
      </div>
    </div>);
};

export default Page;
