body {
  background-color: var(--common_fg_press_color);
}

.product-page-container {
  background-color: var(--common_fg_press_color);
  width: 100%;
  margin-left: auto;
  box-sizing: border-box;
  margin-right: auto;
  display: block;
  padding: 16px 16px 24px 16px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
  min-height: 100vh;

  .product-container {
    background-color: var(--common_fg_color);
    border-radius: 16px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; // Important for flex child to shrink
  }

  .product-detail {
    display: flex;

    .img-container {
      position: relative;
      overflow: hidden;
      margin-right: 8px;
      width: 120px;
      height: 120px;
      border-radius: 6px;
      font-size: 0;
      .product-platform {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 32px;
        font-size: 12px;
        padding: 4px 6px;
        color: #FFFFFF;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(10px);
        word-break: break-word;
      }
    }

    .discription-container {
      flex: 1 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .price-title {
        color: var(--common_level1_base_color);
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: 14px;
      }

      .price {
        color: var(--common_level1_base_color);
        font-size: 12px;
        font-style: normal;
        font-weight: 300;

        span {
          color: #FF0E53;
          font-size: 20px;
          font-style: normal;
          font-weight: 600;
        }
      }

      .shopName {
        color: var(--common_level2_base_color);
        margin-top: 8px;
        font-size: 12px;
        font-style: normal;
      }

      .tag-wrap {
        display: flex;
        margin-top: 4px;
        width: 100%;
        align-items: center;
        justify-content: start;

        .product-stats {
          max-width: 100%;
          color: #666;
          font-size: 0;
          .value {
            margin-right: 6px;
            margin-bottom: 4px;
            max-width: 100%;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 2.5px 4px;
            font-size: 12px;
            border: 0.5px solid #FF0E53;
            border-radius: 4px;
            color: var(--extended_red5_color, #FF0E53);
          }
        }
      }

      .product-title {
        width: 100%;
        color: var(--common_level1_base_color);
        text-overflow: ellipsis;
        font-family: Hiragino Sans;
        white-space: wrap;
        overflow: hidden;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 13px;
        font-style: normal;
        font-weight: 300;
        line-height: 18px;
      }

      .product-price {
        display: flex;
        flex-direction: column;

        .origin {
          color: #666;
          font-family: Hiragino Sans;
          font-size: 10px;
          font-style: normal;
          font-weight: 300;
          line-height: 14px;
        }

        .price {
          font-size: 20px;
          color: #FF0E53;
          font-weight: 600;
        }
      }
    }
  }

  .product-1688 {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 16px;
    &-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
      color: var(--common_level1_base_color);
    }
    &-info {
      color: var(--common_level3_base_color);
      background-color: var(--shadow_M_color);
      margin-left: 8px;
    }
  }

  .product-list {
    flex: 1;
    min-height: 0; // Important for flex child to shrink
    display: flex;
    flex-direction: column;
  }
}
