import React, { useEffect } from 'react';
import { Skeleton, WhiteSpace, WingBlank } from 'dingtalk-design-mobile';
import { i18next } from '@ali/dingtalk-i18n';
import { setPageTitle } from '@/utils/jsapi';
import './index.less';

const SkeletonComponent: React.FC = () => {
  useEffect(() => {
    setPageTitle(i18next.t('j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing'));
  }, []);

  return (
    <div className="skeleton-container">
      <WingBlank>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <Skeleton type="text" animated />
        <WhiteSpace />
        <Skeleton type="text" width="60%" animated />
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
        <WhiteSpace />
        <div style={{ display: 'flex' }}>
          <Skeleton type="card" animated width="20%" height={'100px'} />
          <Skeleton type="text" width="60%" animated style={{ marginLeft: '12px' }} />
        </div>
      </WingBlank>
    </div>
  );
};

export default SkeletonComponent;
