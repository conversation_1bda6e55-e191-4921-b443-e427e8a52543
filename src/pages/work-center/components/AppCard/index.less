@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.app-card {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 80px;
  cursor: pointer;
  padding: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      transform: none;
      .common_box_shadow_s();
    }
  }

  &__badge {
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background: @theme_danger1_color;
    color: @common_bg_color;
    .common_tiny_text_style_mob();
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
    font-weight: 500;
    z-index: 1;
  }

  &__icon {
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    width: 40px;
    border-radius: 33%;
    padding: 4px;
  }

  &__name {
    .common_body_text_style_mob();
    color: @common_level1_base_color;
    line-height: 18px;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
