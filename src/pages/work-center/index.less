@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.work-center {
  min-height: 100vh;
  background: @common_bg_color;
  padding: 16px;

  &__container {
    max-width: 1200px;
    margin: 0 auto;
  }

  &__category {
    margin-bottom: 12px;
    border-radius: 16px;
    padding: 16px;
    background-color: @common_bg_z1_color;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__category-title {
    /* 正文/17B_Body */
    font-size: 17px;
    font-weight: 500;
    line-height: 24px;
    color: @common_level1_base_color;
    margin-bottom: 16px;
    padding-left: 4px;
  }

  &__app-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}
