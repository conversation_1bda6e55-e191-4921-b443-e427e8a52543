import React from 'react';
import { useTitle } from '@/hooks/useTitle';
import { IAppEntry, IAppCategory } from './types';
import AppCard from './components/AppCard';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import './index.less';

const WorkCenter: React.FC = () => {
  useTitle('工作台');
  // 应用数据直接写死在组件中
  const appData: IAppCategory[] = [
    {
      id: 'ecommerce',
      title: '电商应用',
      description: '电商相关的应用工具',
      apps: [
        {
          id: 'data-dashboard',
          name: '数据看板',
          icon: '📊',
          url: '/data-dashboard',
          description: '查看电商数据统计',
        },
        {
          id: 'omnichannel',
          name: '全渠道沟通',
          icon: '💬',
          url: '/omnichannel',
          description: '多渠道客户沟通管理',
        },
        {
          id: 'order-data',
          name: '订单数据',
          icon: '📦',
          url: '/order-data',
          description: '订单数据管理和分析',
        },
        {
          id: 'advertising',
          name: '广告',
          icon: '📢',
          url: '/advertising',
          description: '广告投放和管理',
        },
      ],
    },
    {
      id: 'general',
      title: '通用应用',
      description: '通用办公应用',
      apps: [
        {
          id: 'multidimensional-table',
          name: '多维表',
          icon: '📋',
          url: '/multidimensional-table',
          description: '多维数据表格',
        },
        {
          id: 'document',
          name: '文档',
          icon: '📄',
          url: '/document',
          description: '文档管理和编辑',
        },
        {
          id: 'attendance',
          name: '考勤',
          icon: '✅',
          url: '/attendance',
          description: '员工考勤管理',
        },
        {
          id: 'approval',
          name: '审批',
          icon: '🔴',
          url: '/approval',
          description: '工作流程审批',
        },
        {
          id: 'payroll',
          name: '算薪',
          icon: '💰',
          url: '/payroll',
          description: '薪资计算和管理',
        },
        {
          id: 'meeting',
          name: '会议',
          icon: '🎥',
          url: '/meeting',
          description: '会议安排和视频会议',
        },
        {
          id: 'mailbox',
          name: '邮箱',
          icon: '📧',
          url: '/mailbox',
          description: '企业邮箱管理',
        },
        {
          id: 'more',
          name: '更多',
          icon: '⋯',
          url: '/more',
          description: '更多应用',
        },
      ],
    },
  ];

  // 获取当前URL中的corpId
  const getCurrentCorpId = (): string | null => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('corpId');
  };

  // 处理应用点击
  const handleAppClick = async (app: IAppEntry) => {
    if (app.url) {
      try {
        // 获取当前corpId
        const corpId = getCurrentCorpId();

        // 构建完整的URL，支持corpId替换
        let finalUrl = app.url;
        if (corpId) {
          const url = new URL(app.url, window.location.origin);
          url.searchParams.set('corpId', corpId);
          finalUrl = url.toString();
        }

        // 使用钉钉JSAPI打开链接
        await $openLink({ url: finalUrl });
      } catch (error) {
        console.error('打开链接失败:', error);
        // 降级处理：如果JSAPI失败，使用普通方式打开
        if (app.onClick) {
          app.onClick(app);
        }
      }
    } else if (app.onClick) {
      app.onClick(app);
    }
  };

  return (
    <div className="work-center">
      <div className="work-center__container">
        {appData.map((category) => (
          <div key={category.id} className="work-center__category">
            <h2 className="work-center__category-title">{category.title}</h2>
            <div className="work-center__app-grid">
              {category.apps.map((app) => (
                <AppCard
                  key={app.id}
                  app={app}
                  onClick={() => handleAppClick(app)}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkCenter;
