// 应用入口类型定义
export interface IAppEntry {
  id: string;
  name: string;
  icon: string;
  url?: string;
  onClick?: (app: IAppEntry) => void;
  description?: string;
  badge?: string | number;
  disabled?: boolean;
}

// 应用分类类型定义
export interface IAppCategory {
  id: string;
  title: string;
  apps: IAppEntry[];
  description?: string;
}

// 应用配置类型
export interface IAppConfig {
  categories: IAppCategory[];
  version: string;
  lastUpdated: string;
}
