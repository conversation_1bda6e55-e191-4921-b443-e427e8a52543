/**
 * @desc 判断是否 debug 模式
 */
const isDebugMode = window.location.href?.indexOf('debug') > -1;

/**
 * @desc AI调试工具函数，仅在URL中包含debug参数时输出日志
 * <AUTHOR>
 * @param {...any} args 需要打印的参数列表
 */
export function log(...args: any) {
  if (!isDebugMode) {
    return;
  }
  console.log.apply(null, args);
}

log.error = function (...args: any) {
  if (!isDebugMode) {
    return;
  }
  console.error.apply(null, args);
};

log.warn = function (...args: any) {
  if (!isDebugMode) {
    return;
  }
  console.warn.apply(null, args);
};

log.info = function (...args: any) {
  if (!isDebugMode) {
    return;
  }
  console.info.apply(null, args);
};
