import { i18next } from '@ali/dingtalk-i18n';
import { EPlatform } from '@/common/types';

// Product ranking item interface
export interface IProductRanking {
  title: string;
  key: string;
  rankingName?: string;
  id?: string;
  rankingNameJp?: string;
}

// Get sales list title based on reportType
export const getSalesListTitle = (reportType: string) => {
  switch (reportType) {
    case 'daily':
      return i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList');
    case 'weekly':
      return i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList');
    case 'monthly':
      return i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList');
    default:
      return i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList');
  }
};

// Get sales list content based on reportType
export const getSalesListContent = (reportType: string) => {
  switch (reportType) {
    case 'daily':
      return i18next.t('j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily');
    case 'weekly':
      return i18next.t('j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly');
    case 'monthly':
      return i18next.t('j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly');
    default:
      return i18next.t('j-agent-web_pages_cardTips_ThePlatformSoldTheMost');
  }
};

// Generate product rankings list for cardTips page
export const generateCardTipsList = (platform: string, reportType: string) => {
  const baseList = [];

  // Add opportunityProductList only if platform is not amazon
  if (platform !== 'amazon') {
    baseList.push({
      title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList'),
      content: i18next.t('j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll'),
      key: 'opportunityProductList',
    });
  }

  // For Amazon platform, swap the order of soaringProductList and newProductList
  if (platform === 'amazon') {
    baseList.push(
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        content: i18next.t('j-agent-web_pages_cardTips_NewProductsWithTheLargest'),
        key: 'newProductList',
      },
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        content: i18next.t('j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe'),
        key: 'soaringProductList',
      },
    );
  } else {
    baseList.push(
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        content: i18next.t('j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe'),
        key: 'soaringProductList',
      },
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        content: i18next.t('j-agent-web_pages_cardTips_NewProductsWithTheLargest'),
        key: 'newProductList',
      },
    );
  }

  // Add salesProductList at the end
  baseList.push({
    title: getSalesListTitle(reportType),
    content: getSalesListContent(reportType),
    key: 'salesProductList',
  });

  return baseList;
};

// Generate product tabs for ProductPage components
export const generateProductTabs = (reportType: string, platform: EPlatform | string) => {
  const tabs = [];

  // Add opportunityProductList only if platform is not amazon
  if (platform !== 'amazon') {
    tabs.push({
      title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList'),
      key: 'opportunityProductList',
    });
  }

  // For Amazon platform, swap the order of soaringProductList and newProductList
  if (platform === 'amazon') {
    tabs.push(
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        key: 'newProductList',
      },
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        key: 'soaringProductList',
      },
    );
  } else {
    tabs.push(
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        key: 'soaringProductList',
      },
      {
        title: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        key: 'newProductList',
      },
    );
  }

  // Add salesProductList at the end
  tabs.push({
    title: getSalesListTitle(reportType),
    key: 'salesProductList',
  });

  return tabs;
};

// Generate basic product rankings array (for origin pages)
export const createProductRankingsArray = (platform?: EPlatform | string) => {
  const rankings = [];

  // Add opportunityProductList only if platform is not amazon
  if (!platform || platform !== 'amazon') {
    rankings.push({
      rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList'),
      id: 'opportunityProductList',
      rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList'),
    });
  }

  // For Amazon platform, swap the order of soaringProductList and newProductList
  if (platform === 'amazon') {
    rankings.push(
      {
        rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        id: 'newProductList',
        rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
      },
      {
        rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        id: 'soaringProductList',
        rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
      },
    );
  } else {
    rankings.push(
      {
        rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
        id: 'soaringProductList',
        rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList'),
      },
      {
        rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
        id: 'newProductList',
        rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList'),
      },
    );
  }

  // Add salesProductList at the end
  rankings.push({
    rankingName: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList'),
    id: 'salesProductList',
    rankingNameJp: i18next.t('j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList'),
  });

  return rankings;
};
