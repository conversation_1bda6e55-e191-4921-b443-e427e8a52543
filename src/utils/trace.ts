import UT from '@ali/dingtalk-jsapi/api/biz/util/ut';
import { log } from './console';
import { uploadUserTrack } from '@/apis';

const PageName = 'J_h5_page';

/**
 * 发送埋点
 * @param key 埋点key
 * @param value 埋点value
 */
export function sendUT(key?: string, value: any = {}) {
  const _key = key ? `${PageName}_${key}` : PageName;
  log('sendUT: ', _key, value);
  UT({
    key: _key,
    value,
    // @ts-ignore
    ddWebTrack: true,
  });
  uploadUserTrack({
    action: key,
    detail: JSON.stringify(value),
    categoryPath: PageName,
  });
}
