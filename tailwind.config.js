const { formatDiagnosticsWithColorAndContext } = require('typescript')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/**/*.tpl',
  ],
  theme: {
    extend: {
      spacing: {
        0: '0',
        1: '1px',
        2: '2px',
        4: '4px',
        5: '5px',
        6: '6px',
        7: '7px',
        8: '8px',
        10: '10px',
        12: '12px',
        13: '13px',
        15: '15px',
        16: '16px',
        20: '20px',
      },
    },
    backgroundColor: (theme) => ({
      ...theme('colors'),
      common_fg_color: 'var(--common_fg_color, rgba(255, 255, 255, 1))',
      common_bg_color: 'var(--common_bg_color, rgba(242, 242, 246, 1))',
      common_bg_z1_color: 'var(--common_bg_z1_color, #fff)',
      theme_primary1_color: 'var(--theme_primary1_color, rgba(0,127,255,1))',
      theme_primary3_color: 'var(--theme_primary3_color, rgba(0,127,255,0.12))',
      common_fg_z1_color: 'var(--common_fg_z1_color, rgba(255,255,255,1))',
      common_stamp_color: 'var(--common_stamp_color, rgba(255,255,255, 0.6))',
      common_fg_press_color: 'var(--common_fg_press_color, rgba(246,246,246,1))',
      common_blue1_color: 'var(--common_blue1_color, #007fff)',
      common_blue3_color: 'var(--common_blue3_color, rgba(0, 127, 255, 0.12))',
      common_red1_color: 'var(--common_red1_color, #ff5219)',
    }),
    borderRadius: {
      none: '0',
      2: '2px',
      4: '4px',
      6: '6px',
      8: '8px',
      full: '9999px',
    },
    textColor: (theme) => ({
      ...theme('colors'),
      common_white1_color: 'var(--common_white1_color, rgba(255,255,255,1))',
      common_blue1_color: 'var(--common_blue1_color, #007fff)',
      common_level1_base_color: 'var(--common_level1_base_color, rgba(23,26,29,1))',
      common_level2_base_color: 'var(--common_level2_base_color, rgba(23,26,29,0.60))',
      common_level3_base_color: 'var(--common_level3_base_color, rgba(23,26,29,0.40))',
      common_level4_base_color: 'var(--common_level4_base_color, rgba(23,26,29,0.24))',
    }),
    fontSize: {
      12: '12px',
      14: '14px',
      16: '16px',
      20: '20px',
    },
    borderWidth: {
      DEFAULT: '1px',
    },
    borderColor: (theme) => ({
      ...theme('colors'),
      gray1: 'rgba(126,134,142,0.16)',
      common_blue1_color: 'var(--common_blue1_color, #007fff)',
      common_blue3_color: 'var(--common_blue3_color, rgba(0, 127, 255, 0.12))',
      common_line_hard_color: 'var(--common_line_hard_color, rgba(126, 134, 142, 0.24))',
    }),
    lineHeight: {
      22: '22px',
      26: '26px',
      28: '28px',
      32: '32px',
    },
    fontFamily: {
      pingfang_sc: 'PingFang SC,sans-serif',
    },
  },
};
