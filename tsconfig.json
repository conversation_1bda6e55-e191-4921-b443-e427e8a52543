{
  "compilerOptions": {
    "target": "ES2018",
    "module": "commonjs",
    "moduleResolution": "node",
    "noImplicitAny": false,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "inlineSourceMap":true,
    "noImplicitThis": false,
    "noUnusedLocals": true,
    "stripInternal": true,
    "pretty": true,
    "declaration": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "baseUrl": "./",
    "paths": {
      "*": ["*"],
      "@/*": ["src/*"],
    }
  },
  "include": [
    "types/**/*",
    "src/**/*"
  ],
  "exclude": [
    "node_modules"
  ]
}
