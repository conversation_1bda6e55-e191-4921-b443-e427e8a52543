const HtmlWebpackPlugin = require('html-webpack-plugin');
const {
  merge,
} = require('webpack-merge');
const rootPath = require('../utils/rootPath');

const portFinderSync = require('portfinder-sync');
const baseWebpackConfig = require('./webpack.base.config');
const pageList = require('../utils/pageList'); // 获取多页面入口
module.exports = merge(baseWebpackConfig, {
  devtool: process.env.NODE_ENV === 'development' 
    ? 'eval-source-map'  // 开发环境，提供良好的调试体验
    : 'source-map',  
  devServer: {
    historyApiFallback: true,
    injectClient: false,
    hot: false,
    // open: true,
    port: portFinderSync.getPort(8080),
    host: '0.0.0.0',
    // allowedHosts: [
    //   '.dingtalk.com',
    //   "localhost",
    // ],
    writeToDisk: true
  },

  plugins: [
    ...pageList.map((page) => { 
      return new HtmlWebpackPlugin({
        filename: `./${page}/index.html`,
        template: rootPath('template.html'),
        inject: false,
        chunks: [page],
        chunksSortMode: 'manual',
        
        env: 'development',
      }); 
    })
  ],
});
