const { merge } = require('webpack-merge');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserJSPlugin = require('terser-webpack-plugin');
const baseWebpackConfig = require('./webpack.base.config');
const isAnalyze = process.argv?.includes('analyze');
const rootPath = require('../utils/rootPath');
const pageList = require('../utils/pageList'); // 获取多页面入口
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = merge(baseWebpackConfig, {
  mode: 'production',
  plugins: [
    isAnalyze
      ? new BundleAnalyzerPlugin({
          analyzerPort: 'auto',
        })
      : null,
      ...pageList.map((page) => {
        return new HtmlWebpackPlugin({
          filename: `./${page}/index.html`,
          template: rootPath('template.html'),
          inject: false,
          chunks: [page],
          chunksSortMode: 'manual',
          env: 'production',
        });
      })
  ].filter(Boolean),
  // 优化配置
  optimization: {
    minimizer: [
      // 压缩css
      new CssMinimizerPlugin(),
      // 压缩js
      new TerserJSPlugin({
        extractComments: false, // 不将注释提取到单独的文件中
        parallel: true, // 开启多线程压缩
        terserOptions: {
          compress: {
            // pure_funcs: ['console.log'],
          },
        },
      })
    ]
  },
});
